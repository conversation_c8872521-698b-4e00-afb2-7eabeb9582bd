<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Requests\Shared\DateRangeRequest;
use App\Models\RepairOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashbordChartController extends Chart
{
    public function RepairOrderChartDetails(DateRangeRequest $request)
    {
        $startDate = Carbon::parse($request->start_date)->startOfDay();
        $endDate = Carbon::parse($request->end_date)->endOfDay();

        // Get current date range data
        $results = RepairOrder::select(
            DB::raw('DATE(StatusDate) as date'),
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(totalro) as ammount')
        )
            ->whereBetween('StatusDate', [$startDate, $endDate])
            ->groupBy(DB::raw('DATE(StatusDate)'))
            ->orderBy('date', 'asc')
            ->get();

        [$prevStartDate, $prevEndDate] = $this->getPeriod($startDate, $endDate);

        // Get previous period data
        $prevResults = RepairOrder::select(
            DB::raw('COUNT(*) as count'),
            DB::raw('SUM(totalro) as ammount')
        )
            ->whereBetween('StatusDate', [$prevStartDate, $prevEndDate])
            ->first();

        // Initialize the daily data array with zero values
        $ro_data = [];
        $ro_totals = [
            'count' => 0,
            'ammount' => 0,
        ];
        $ro_status = [
            'count' => 0,
            'ammount' => 0,
        ];
        for ($date = $startDate; $date->lte($endDate); $date->addDay()) {
            $ro_data[$date->toDateString()] = [
                'date' => $date->toDateString(),
                'count' => 0,
                'ammount' => 0,
            ];
        }

        // Merge the query results with the daily data
        foreach ($results as $result) {
            $ro_data[$result->date] = [
                'date' => $result->date,
                'count' => $result->count,
                'ammount' => $result->ammount,
            ];

            $ro_totals['count'] += $result->count;
            $ro_totals['ammount'] += $result->ammount;
        }

        // Convert the associative array to a numeric array
        $ro_data = array_values($ro_data);

        $ro_status['count'] = $ro_totals['count'] - $prevResults->count;
        $ro_status['ammount'] = $ro_totals['ammount'] - $prevResults->ammount;

        return response()->json([
            'ro_data' => $ro_data,
            'ro_totals' => $ro_totals,
            'ro_status' => $ro_status,
            'previous_result' => $prevResults,
        ], 200);
    }

    public function AccountsChartDetails(DateRangeRequest $request)
    {
        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();

        $new_accounts = $this->getAccountPackages($start_date, $end_date);

        $silver = $new_accounts['silver'] ?? 0;
        $gold = $new_accounts['gold'] ?? 0;
        $platinum = $new_accounts['platinum'] ?? 0;
        $premier = $new_accounts['premier'] ?? 0;
        $premier_plus = $new_accounts['premierPlus'] ?? 0;

        [$previous_start_date, $previous_end_date] = $this->getPeriod($start_date, $end_date);

        $previous_accounts = $this->getAccountPackages($previous_start_date, $previous_end_date);

        $silver_vs_previous = $silver - $previous_accounts['silver'];
        $gold_vs_previous = $gold - $previous_accounts['gold'];
        $platinum_vs_previous = $platinum - $previous_accounts['platinum'];
        $premier_vs_previous = $premier - $previous_accounts['premier'];
        $premier_plus_vs_previous = $premier_plus - $previous_accounts['premierPlus'];

        return response()->json([
            'silver' => [
                'total' => $silver,
                'increase' => $silver_vs_previous,
            ],
            'gold' => [
                'total' => $gold,
                'increase' => $gold_vs_previous,
            ],
            'platinum' => [
                'total' => $platinum,
                'increase' => $platinum_vs_previous,
            ],
            'premier' => [
                'total' => $premier,
                'increase' => $premier_vs_previous,
            ],
            'premierPlus' => [
                'total' => $premier_plus,
                'increase' => $premier_plus_vs_previous,
            ],
        ], 200);
    }

    public function ssShops(DateRangeRequest $request)
    {
        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();

        $data = $this->getShops($start_date, $end_date);

        [$previous_start_date, $previous_end_date] = $this->getPeriod($start_date, $end_date);

        $previous_data = $this->getShops($previous_start_date, $previous_end_date);

        return response()->json([
            'shops' => $data,
            'previous_shops' => $previous_data,
        ], 200);
    }

    public function msShops(DateRangeRequest $request)
    {
        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();

        $data = $this->getShops($start_date, $end_date, 'sbpent');

        [$previous_start_date, $previous_end_date] = $this->getPeriod($start_date, $end_date);

        $previous_data = $this->getShops($previous_start_date, $previous_end_date, 'sbpent');

        return response()->json([
            'shops' => $data,
            'previous_shops' => $previous_data,
        ], 200);
    }

    public function ARO(DateRangeRequest $request)
    {
        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();

        $data = $this->getARO($start_date, $end_date);

        [$previous_start_date, $previous_end_date] = $this->getPeriod($start_date, $end_date);

        $previous_data = $this->getARO($previous_start_date, $previous_end_date);

        return response()->json([
            'aro' => $data['average_totalro'],
            'previous_aro' => $previous_data['average_totalro'],
        ], 200);
    }

    public function bossPay(DateRangeRequest $request)
    {
        $start_date = Carbon::parse($request->start_date)->startOfDay();
        $end_date = Carbon::parse($request->end_date)->endOfDay();

        $data = $this->getBossPay($start_date, $end_date);

        [$previous_start_date, $previous_end_date] = $this->getPeriod($start_date, $end_date);

        $previous_data = $this->getBossPay($previous_start_date, $previous_end_date);

        return response()->json([
            'boss_pay' => $data,
            'previous_boss_pay' => $previous_data,
        ], 200);
    }
}
