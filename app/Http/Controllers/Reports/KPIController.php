<?php

namespace App\Http\Controllers\Reports;

use App\Models\Company;
use App\Models\Payments;
use App\Models\RepairOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class KPIController extends Controller
{
    public function index()
    {
        // select distinct dtime from monthcapture order by dtime desc
        $dDates = DB::table('monthcapture')->select('dtime')
            ->distinct('dtime')
            ->orderBy('dtime', 'desc')
            ->distinct('dtime')
            ->pluck('dtime')
            ->toArray();

        return Inertia::render('report/KPI', [
            'dDates' => $dDates,
        ]);
    }

    public function standardKPI(Request $request)
    {
        if ($request->expectsJson()) {

            $today = getdate();
            $year = $today['year'];

            $sd = $year.'-01-01';
            $ed = $year.'-12-31';

            $activeshops = Company::select(DB::raw('COUNT(*) as count')) // Current Active SSO
                ->where('status', 'active')
                ->where('shopid', '!=', 'demo')
                ->where('readonly', 'no')
                ->where('package', 'Paid')
                ->count();

            $readonlys = Company::select(DB::raw('COUNT(*) as count')) // Read Only Shops
                ->where('shopid', '!=', 'demo')
                ->where('readonly', 'yes')
                ->where('package', 'Paid')
                ->where('duplicate', '!=', 'y')
                ->count();

            $entcount = DB::connection('sbpent')->table('company') // Enterprise MSO Shops
                ->whereNotIn('shopid', ['10', 'srtraining', 'BC9999'])
                ->where('status', 'active')
                ->where('enterpriseid', '!=', 2)
                ->count();

            $tshops = $activeshops + $entcount; // Total Shop Count

            $shopsforyear = 0;
            for ($j = 1; $j <= 12; $j++) {
                $cysd = date('Y-m-d', strtotime(date('Y').'-'.$j.'-01'));
                $cyed = date('Y-m-d', strtotime(date('Y').'-'.$j.'-31'));

                $datestarteddate = date('Y-m-d', strtotime('-2 months', strtotime($cysd)));

                $shopsforyearCount = Company::where('readonly', 'no')
                    ->whereBetween('dateofacceptance', [$cysd, $cyed])
                    ->where('datestarted', '>=', $datestarteddate)
                    ->count();

                $shopsforyear += $shopsforyearCount; // YTD Sold
            }

            $days = date('z') + 1;
            $shopsperday = round($shopsforyear / $days, 2); // YTD Daily
            $paceshops = round($shopsperday * 365, 0); // YTD Pace
            $remainingdays = 365 - $days;

            $lysd = date('Y-m-d', strtotime($sd.' -1 year'));
            $datestarteddately = date('Y-m-d', strtotime('-2 months', strtotime($lysd)));
            $lyed = date('Y-m-d', strtotime(date('Y-m-d').' -1 year'));

            $shopsforlastyear = Company::whereBetween('dateofacceptance', [$lysd, $lyed]) // Last YTD
                ->where('readonly', 'no')
                ->where('datestarted', '>=', $datestarteddately)
                ->count();

            $pacetotal = round($tshops + ($remainingdays * $shopsperday), 0);

            $activecacnt = Company::where('status', 'active') // Canadian Shops
                ->where('shopid', '!=', 'demo')
                ->where('readonly', 'no')
                ->where('package', 'Paid')
                ->whereRaw("companyzip not REGEXP '^[0-9]+$'")
                ->count();

            $susshops = 0;

            $shopRepairOrders = RepairOrder::select('shopid', DB::raw('COUNT(*) as rocount'))
                ->whereIn('shopid', function ($query) use ($ed) {
                    $query->select('shopid')
                        ->from('company')
                        ->where('shopid', '!=', 'demo')
                        ->where('duplicate', '!=', 'y')
                        ->whereBetween('dateofacceptance', ['2012-01-01', $ed])
                        ->where('package', 'Paid')
                        ->where('status', 'SUSPENDED');
                })
                ->where('status', 'closed')
                ->groupBy('shopid')
                ->having('rocount', '>', 30)  // Only shops with more than 30 closed repair orders
                ->get();

            $susshops = $shopRepairOrders->count(); // Suspended Shops

            $totalchurn = 0;
            if (($activeshops + $susshops) > 0) {
                $totalchurn = ($susshops / ($tshops + $susshops)) * 100.00;
                $totalchurn = round($totalchurn, 0).'%';
            }

            $standardKPI = [
                'activeSSO' => $activeshops,
                'readOnlyShops' => $readonlys,
                'enterpriseMSOShops' => $entcount,
                'totalShopCount' => $tshops,
                'currentYearPace' => $pacetotal,
                'canadianShops' => $activecacnt,
                'suspendedShops' => $susshops,
                'churn' => $totalchurn.'%',
                'YTDSold' => $shopsforyear,
                'YTDDaily' => $shopsperday,
                'YTDPace' => $paceshops,
                'LastYTD' => $shopsforlastyear,
            ];

            return response()->json($standardKPI, 200);
        } else {
            abort(404);
        }
    }

    public function mainKPI(Request $request)
    {
        if ($request->expectsJson()) {
            $ds = $request->data_date;
            $startDate = $request->start_date;
            $endDate = $request->end_date;

            $monthCaptures = DB::table('monthcapture')
                ->select([
                    'shopid',
                    'plan',
                ])
                ->where('shoptype', 'sso')
                ->where('accounttype', 'Paid')
                ->whereBetween('dateaccepted', [$startDate, $endDate])
                ->where('dtime', $ds)
                ->get();

            $newShops = 0;
            $newRev = 0;

            foreach ($monthCaptures as $monthCapture) {
                $newShops++;

                if ($monthCapture->plan == 'silver') {
                    $newRev += 109;
                } elseif ($monthCapture->plan == 'gold') {
                    $newRev += 199;
                } elseif ($monthCapture->plan == 'platinum') {
                    $newRev += 299;
                } elseif ($monthCapture->plan == 'premier') {
                    $newRev += 399;
                } elseif ($monthCapture->plan == 'premier plus') {
                    $newRev += 499;
                }
            }

            $monthCaptureSuspdended = DB::table('monthcapture')
                ->select([
                    'shopid',
                    'plan',
                ])
                ->where('shoptype', 'sso')
                ->where('accounttype', 'Paid')
                ->whereBetween('datesuspended', [$startDate, $endDate])
                ->where('dtime', $ds)
                ->get();

            $cntr = 1;
            $shoplist = [];

            foreach ($monthCaptureSuspdended as $monthCapture) {
                $cntr++;
                $shoplist[] = $monthCapture->shopid;
            }

            $repairOrders = RepairOrder::select([
                'shopid',
                DB::raw('COUNT(*) as count'),
            ])
                ->where('status', 'closed')
                ->where('rotype', '!=', 'no approval')
                ->whereIn('shopid', $shoplist)
                ->groupBy('shopid')
                ->get();

            $churncnt = 0;
            $churnlist = [];

            foreach ($repairOrders as $repairOrder) {
                if ($repairOrder->count > 30) {
                    $churncnt++;
                    $churnlist[] = $repairOrder->shopid;
                }
            }

            $prevmonthsd = date('Y-m', strtotime('-1 months', strtotime($startDate))).'-01';
            $prevmonthed = date('Y-m-t', strtotime($prevmonthsd));

            $mochurnrev = Payments::whereIn('shopid', $churnlist)
                ->whereBetween('paymentdate', [$prevmonthsd, $prevmonthed])
                ->sum('amount');

            $netrev = $newRev - $mochurnrev;

            $monthCaptureTrial = DB::table('monthcapture')
                ->where('shoptype', 'sso')
                ->where('accounttype', 'Trial')
                ->where('STATUS', 'active')
                ->whereBetween('datestarted', [$startDate, $endDate])
                ->where('dtime', $ds)
                ->count();

            $currtrials = Company::where('package', 'trial')
                ->where('datesuspended', '0000-00-00')
                ->count();

            $forSQLS = DB::table('monthcapture')
                ->select('shopid')
                ->where('accounttype', 'Trial')
                ->where('STATUS', 'active')
                ->where('dtime', $ds)
                ->distinct('shopid')
                ->get();

            $tshoplist = [];
            $totaltrialcount = 0;

            foreach ($forSQLS as $forSQL) {
                $tshoplist[] = $forSQL->shopid;
                $totaltrialcount++;
            }

            $sqlcntQuery = RepairOrder::select(DB::raw('COUNT(*) as count'))
                ->whereIn('shopid', $tshoplist)
                ->where('rotype', '!=', 'no approval')
                ->groupBy('shopid')
                ->get();

            $sqlcnt = 0;

            foreach ($sqlcntQuery as $sql) {
                if ($sql->count >= 10) {
                    $sqlcnt++;
                }
            }

            $responseData = [
                'sold' => $newShops,
                'newRevenue' => $newRev,
                'suspended' => $cntr,
                'churned' => $churncnt,
                'churnedRevenue' => $mochurnrev,
                'netRevenueGain' => $netrev,
                'newTrialsCreated' => $totaltrialcount,
                'currentTotalTrials' => $currtrials,
                'SQLS' => $sqlcnt,
            ];

            return response()->json($responseData, 200);
        } else {
            abort(404);
        }
    }

    public function accounts()
    {
        $packages = Company::select([
            DB::raw('COUNT(newpackagetype) as count'),
            'newpackagetype',
        ])
            ->where('status', '!=', 'suspended')
            ->where('shopid', '!=', 'demo')
            ->where('package', 'Paid')
            ->groupBy('newpackagetype')
            ->orderBy('newpackagetype')
            ->get();

        $packages = $packages->pluck('count', 'newpackagetype')->toArray();

        $merchantAccounts = Company::select(DB::raw('COUNT(*) as c, merchantaccount'))
            ->where('status', 'active')
            ->where('shopid', '!=', 'demo')
            ->where('package', 'Paid')
            ->groupBy('merchantaccount')
            ->orderBy('merchantaccount')
            ->get();

        $merchantAccountsData = [
            'payment360' => 0,
            'authorize' => 0,
            'cardknox' => 0,
            'tnp' => 0,
            'none' => 0,
        ];

        foreach ($merchantAccounts as $index => $result) {
            switch ($index) {
                case 0:
                    $merchantAccountsData['payment360'] = $result->c;
                    break;
                case 1:
                    $merchantAccountsData['authorize'] = $result->c;
                    break;
                case 2:
                    $merchantAccountsData['cardknox'] = $result->c;
                    break;
                case 3:
                    $merchantAccountsData['none'] = $result->c;
                    break;
                case 4:
                    $merchantAccountsData['tnp'] = $result->c;
                    break;
            }
        }

        return response()->json([
            'packages' => $packages,
            'merchantAccounts' => $merchantAccountsData,
        ]);
    }
}
