<?php

namespace App\Http\Controllers\Shop;

use App\Models\Company;
use App\Models\CompanyComments;
use App\Models\Settings;
use Carbon\Carbon;
use DateTime;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;

abstract class Controller
{
    protected $shopid;

    protected function redirect($message)
    {
        return Inertia::render('shop/Show', [
            'flash' => [
                'message' => $message,
            ],
        ]);
    }

    protected function asDollars($number)
    {
        if (! is_numeric($number)) {
            return null;
        }

        return '$'.number_format($number, 2, '.', ',');
    }

    protected function formatPhoneNumber($phoneNumber)
    {
        $phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);

        if (strlen($phoneNumber) == 10) {
            return '('.substr($phoneNumber, 0, 3).') '.substr($phoneNumber, 3, 3).'-'.substr($phoneNumber, 6);
        } else {
            return $phoneNumber;
        }
    }

    protected function formatDate($dateString)
    {
        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $date = new DateTime($dateString);
        $day = $date->format('j');
        $month = $months[$date->format('n') - 1];
        $year = $date->format('Y');

        return "{$month}-{$day}-{$year}";
    }

    protected function getCompanies($latest = false)
    {
        $companies = Cache::remember('companies_list'.($latest ? '_latest' : '').Auth::user()->role, now()->addHours(1), function () use ($latest) {
            $data = Company::select('shopid', 'companyName')
                ->distinct()
                ->whereRaw('LOWER(status) != ?', ['suspended'])
                ->where('shopid', '!=', 'demo')
                ->orderBy('shopid', 'asc')
                ->when($latest, fn ($query) => $query->orderBy('ts', 'desc'));

            if (Auth::user()->role == 'matco') {
                $data->where('matco', 'yes');
            }

            $data = $data->get()
                ->mapWithKeys(function ($company) {
                    return [$company->shopid => html_entity_decode($company->companyName)];
                })
                ->toArray();

            return $data;
        });

        return $companies;
    }

    protected function getSettings()
    {
        return Settings::select([
            'motor',
            'failedpayment',
            'pph',
            'oss',
        ])
            ->where('shopid', $this->shopid)->first();
    }

    protected function getCompany()
    {
        $data = Company::select([
            'shopid',
            'companyName',
            DB::raw('LOWER(contact) as contact'),
            'dateofacceptance',
            'profitboost',
            'companyPhone',
            'companyEmail',
            DB::raw("CONCAT(CompanyAddress, ' ', CompanyCity, ', ', CompanyState, ' ', CompanyZip) AS address"),
            DB::raw("CONCAT(CompanyCity, '|', CompanyState) AS ac"),
            'newpackagetype as currentPackage',
            'status',
            'carfaxlocation as carfaxID',
            'merchantaccount as creditCardProcessor',
            'onboardinghours as onboardingHours',
            'dashExpiration',
            'trialexpiration',
            'matco',
            'school',
            'package',
            'terminalid',
            'paytoken',
            'cfpid',
            'newpackagetype',
            'merchantid',
            'merchantpassword',
            'timezone',
            'ts',
        ])->find($this->shopid);

        if ($data) {
            $data->dateofacceptance = $data->dateofacceptance !== '0000-00-00'
                ? $this->formatDate($data->dateofacceptance)
                : '';
            $data->companyName = html_entity_decode($data->companyName);
        }

        $companyCreatedDate = Carbon::parse($data['ts']);
        $currentDate = Carbon::now();
        $onboardingActiveDays = $companyCreatedDate->diffInDays($currentDate);

        $data->onboardingAnimation = $onboardingActiveDays <= 14 ? 'on' : 'off';

        return $data;
    }

    protected function getOnboardingWizardDetails()
    {
        $data = DB::table('customer_onboarding')
            ->where('shopid', $this->shopid)
            ->first();

        return $data;
    }

    protected function getContacts()
    {
        $data = DB::table('employees')
            ->select(
                DB::raw('LOWER(CONCAT(EmployeeFirst, " ", EmployeeLast)) as name')
            )
            ->where('shopid', $this->shopid)
            ->where('Active', 'YES')
            ->pluck('name');

        return $data->toArray();
    }

    protected function getLRO()
    {
        $data = DB::table('repairorders')
            ->select('datein as lro')
            ->where('shopid', $this->shopid)
            ->orderBy('datein', 'desc')
            ->first();

        return $data ? $this->formatDate($data->lro) : '';
    }

    protected function getAudit()
    {
        $data = DB::table('audit')
            ->where('shopid', $this->shopid)
            ->where('category', 'employee login')
            ->orderBy('id', 'desc')
            ->first(['useraccount', 'eventdatetime']);

        return $data
            ? $this->formatDate($data->eventdatetime).' By '.strtolower($data->useraccount)
            : '';
    }

    protected function getCompanyCategory()
    {
        $categories = DB::table('mastercompanycategories')
            ->where('category', '<>', '')
            ->pluck('category');

        $levels = DB::table('mastercompanycategories')
            ->where('level', '<>', '')
            ->pluck('level');

        $selected = DB::table('companycategories')
            ->where('shopid', $this->shopid)
            ->first(['category', 'level']);

        return [
            'categories' => $categories,
            'levels' => $levels,
            'selectedLevel' => $selected->level ?? '',
            'selectedCategory' => $selected->category ?? '',
        ];
    }

    protected function getDataTransfer()
    {
        $data = DB::table('datatransfer')
            ->where('shopid', $this->shopid)
            ->first(['type', 'status', 'billing_type']);

        return [
            'type' => $data->type ?? '',
            'status' => $data->status ?? '',
            'billing_type' => $data->billing_type ?? '',
        ];
    }

    protected function getAccountingLink()
    {
        $data = DB::table('backofficekeys')
            ->select('active', 'ts', 'deactivated_on', 'orderid')
            ->where('shopid', $this->shopid)
            ->first();

        if ($data) {
            $data->ts = \Carbon\Carbon::parse($data->ts)
                ->setTimezone('America/Los_Angeles')
                ->format('m/d/Y h:i A');
            $data->deactivated_on = \Carbon\Carbon::parse($data->deactivated_on)
                ->setTimezone('America/Los_Angeles')
                ->format('m/d/Y h:i A');
        }

        return $data;
    }

    protected function getSmsNumber()
    {
        $data = DB::table('smsnumbers')
            ->select('smsnum')
            ->where('shopid', $this->shopid)
            ->first();

        return $data ? $data->smsnum : '';
    }

    protected function storeCompanyComment($comment)
    {
        $user = Auth::user()->name;

        $comment = CompanyComments::create([
            'shopid' => $this->shopid,
            'comment' => $user.': '.$comment,
            'commentdatetime' => Carbon::now()->format('Y-m-d H:i:s'),
        ]);

        return ['comment' => $comment];
    }

    protected function getJoinedShops()
    {
        $data = DB::table('joinedshops')
            ->where('shopid', $this->shopid)
            ->pluck('joinedshopid')
            ->unique()
            ->values()
            ->toArray();

        return $data;
    }

    protected function getAPICompanies()
    {
        $data = Cache::remember('apiLogin_companyNames', 1, function () {
            return DB::table('apilogin')
                ->select('companyName')
                ->distinct()
                ->pluck('companyName')
                ->toArray();
        });

        return $data;
    }

    protected function getCompanyAdds()
    {
        $data = DB::table('companyadds')
            ->select('name')
            ->where('shopid', $this->shopid)
            ->pluck('name')
            ->unique()
            ->values()
            ->toArray();

        return $data;
    }

    protected function getEpicorSuppliers($supplierList = true)
    {
        if ($supplierList) {
            $data = DB::table('epicor')
                ->select('suppliername')
                ->distinct()
                ->pluck('suppliername');
        } else {
            $data = DB::table('epicor')
                ->select(['id', 'suppliername', 'programgroup', 'username', 'addedby'])
                ->where('shopid', $this->shopid)
                ->get();
        }

        return $data;
    }

    protected function getPartsTechShops()
    {
        $data = DB::table('partstechshops')
            ->select([
                'id',
                'userid',
                'uselistprice',
                'apikey',
            ])
            ->where('shopid', $this->shopid)
            ->get();

        return $data;
    }

    protected function getCipDevices()
    {
        $data = DB::table('cipdevices')
            ->select([
                'id',
                'label',
                'hostip',
            ])
            ->where('shopid', $this->shopid)
            ->get();

        return $data;
    }

    protected function getBetaFeatures()
    {
        $betaFeatures = DB::table('beta_features_list')
            ->select([
                'id',
                'feature_name',
            ])
            ->get();

        $shopBetaFeatures = DB::table('beta_features')
            ->where('shopid', $this->shopid)
            ->pluck('feature_id')
            ->toArray();

        return [
            'list' => $betaFeatures,
            'shop' => $shopBetaFeatures,
        ];
    }

    protected function insertIntoAudit($category, $event)
    {
        DB::table('audit')->insert([
            'shopid' => $this->shopid,
            'category' => $category,
            'event' => $event,
            'useraccount' => Auth::user()->email,
            'eventdatetime' => Carbon::now(),
        ]);
    }

    protected function getFlatPrice($package)
    {
        $prices = [
            'silver' => 109.00,
            'gold' => 199.00,
            'platinum' => 299.00,
            'premier' => 399.00,
            'premier plus' => 499.00,
        ];

        return $prices[$package] ?? '';
    }

    protected function zaphierRequest($status, $date)
    {
        $data = [
            'ShopId' => $this->shopid,
            'Status' => $status,
            'ShopBoss_Suspended_Date' => $date,
            'ShopBoss_Suspended_Reason' => '',
        ];

        $response = Http::post('https://endpoint.scribesoft.com/v1/orgs/47078/requests/22738?accesstoken=03fdbdf1-0c25-4fc8-84fb-472f7a90e4d6', $data);

        if ($response->successful()) {
            $result = $response->body();

            return $response;
        } else {
            $status = $response->status();
            $error = $response->body();

            return $error;
        }
    }
}
