<?php

namespace App\Http\Controllers\Shop;

use App\Http\Requests\Shop\BossBoardAccessUpdateRequest;
use App\Http\Requests\Shop\CardknoxKeysUpdateRequest;
use App\Http\Requests\Shop\CardknoxTerminalUpdateRequest;
use App\Http\Requests\Shop\ChurnUpdateRequest;
use App\Http\Requests\Shop\DVIUpdateRequest;
use App\Http\Requests\Shop\EpicorUpdateRequest;
use App\Http\Requests\Shop\LinkedShopsUpdateRequest;
use App\Http\Requests\Shop\NewOrOldCompaniesRequest;
use App\Http\Requests\Shop\PartsTechUpdateRequest;
use App\Http\Requests\Shop\Payments360UpdateRequest;
use App\Http\Requests\Shop\ReadonlyUpdateRequest;
use App\Http\Requests\Shop\ROIDUpdateRequest;
use App\Http\Requests\Shop\ShopPackageUpdateRequest;
use App\Http\Requests\Shop\TPMSCredsUpdateRequest;
use App\Http\Requests\Shop\TrialExpirationUpdateRequest;
use App\Http\Requests\ShopUpdateRequest;
use App\Http\Requests\User\PasswordRequest;
use App\Models\BackofficeKeys;
use App\Models\Company;
use App\Models\CompanyCategories;
use App\Models\CompanyComments;
use App\Models\CustomerOnboarding;
use App\Models\DataTransfer;
use App\Models\Employees;
use App\Models\RepairOrder;
use App\Models\Settings;
use Carbon\Carbon;
use DateInterval;
use DateTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Http;
use Inertia\Inertia;
use SimpleXMLElement;
use Yajra\DataTables\Facades\DataTables;

class ShopController extends Controller
{
    public function index()
    {
        return Inertia::render('shop/Index', [
            'companies' => $this->getCompanies(),
        ]);
    }

    public function show($shopid)
    {
        $this->shopid = $shopid;

        $userRole = Auth::user()->role == 'matco';
        $company = Company::where('shopid', $shopid)
            ->when($userRole, function ($query) {
                return $query->where('matco', 'yes');
            })->count();

        (empty($this->getCompany()) || $company == 0) && abort(404, 'Shop Not found');

        return Inertia::render('shop/Show', [
            'shopid' => $shopid,
            'companies' => $this->getCompanies(),
            'companyDetails' => $this->getCompany(),
            'lro' => $this->getLRO(),
            'lastLoginDateAndUser' => $this->getAudit(),
            'settings' => $this->getSettings(),
            'companyCategory' => $this->getCompanyCategory(),
            'dataTransfer' => $this->getDataTransfer(),
            'accountLink' => $this->getAccountingLink(),
            'smsNumber' => $this->getSmsNumber(),
            'contacts' => $this->getContacts(),
            'joinedShops' => $this->getJoinedShops(),
            'companyAdds' => $this->getCompanyAdds(),
            'epicorSuppliersList' => $this->getEpicorSuppliers(),
            'epicorSuppliers' => $this->getEpicorSuppliers(false),
            'partTech' => $this->getPartsTechShops(),
            'cardknoxDevices' => $this->getCipDevices(),
            'betaFeatures' => $this->getBetaFeatures(),
            'onboardingWizard' => $this->getOnboardingWizardDetails(),
        ]);
    }

    public function update(ShopUpdateRequest $request, $shopid)
    {
        $company = Company::find($shopid);

        $whoChurnedUpdate = $request->company['status'] != $company['status'];

        // Updates
        $companyData = $request->company;
        $companyData['carfaxlocation'] = $request->company['carfaxlocation'] ? $shopid : 'no';
        $companyData['whochurned'] = $whoChurnedUpdate ? Auth::user()->email : $company->whochurned;
        $company->update($companyData);

        $settings = Settings::find($shopid);
        $settings->update($request->settings);

        CompanyCategories::updateOrCreate(
            ['shopid' => $shopid],
            [...$request->masterCompanyCategories, 'shopid' => $shopid]
        );

        DataTransfer::updateOrCreate(
            ['shopid' => $shopid],
            [...$request->dataTransfer, 'shopid' => $shopid]
        );

        $backofficeKeysData = [
            'active' => $request->backofficeKeys['active'],
        ];

        if ($request->backofficeKeys['active'] === 'yes') {
            $backofficeKeysData['ts'] = \Carbon\Carbon::now();
        } elseif ($request->backofficeKeys['active'] === 'no') {
            $backofficeKeysData['deactivated_on'] = \Carbon\Carbon::now();
        }

        $backofficeKeys = BackofficeKeys::updateOrCreate(
            ['shopid' => $shopid],
            $backofficeKeysData
        );

        if ($request->smsNumber != 'Choose' && $this->getSmsNumber() == '') {
            return $this->saveSMSNumberAction($shopid, $request->smsNumber);
        }

        // Now Add Comments
        $this->shopid = $shopid;

        if ($request->comment) {
            $this->storeCompanyComment($request->comment);
        }

        if ($backofficeKeys->wasChanged('active')) {
            $comment = 'Accounting link turned '.($request->backofficeKeys == 'yes' ? 'on' : 'off');
            $this->storeCompanyComment($comment);
        }

        if ($settings->wasChanged('failedpayment')) {
            $comment = 'Failed payment notification turned '.($settings->failedpayment == 'yes' ? 'on' : 'off');
            $this->storeCompanyComment($comment);
        }

        if ($settings->wasChanged('motor')) {
            $comment = 'Motor turned '.($settings->motor == 'yes' ? 'on' : 'off');
            $this->storeCompanyComment($comment);
        }

        if ($settings->wasChanged('oss')) {
            $comment = 'OSS turned '.($settings->oss == 'yes' ? 'on' : 'off');
            $this->storeCompanyComment($comment);
        }

        if ($company->wasChanged('status')) {
            $comment = 'Shop '.($company->status == 'ACTIVE' ? 'reactivated' : 'suspended');
            $this->storeCompanyComment($comment);
        }

        if ($company->wasChanged('contact')) {
            $comment = 'Shop contact set '.$company->contact;
            $this->storeCompanyComment($comment);
        }

        if ($company->wasChanged('carfaxlocation')) {
            $comment = 'Shop carfax turned '.($company->carfaxlocation == $shopid ? 'on' : 'off');
            $this->storeCompanyComment($comment);
        }

        if ($company->wasChanged('newpackagetype')) {
            $comment = 'Shop package changed to '.($company->newpackagetype);
            $this->storeCompanyComment($comment);
        }

        return redirect()->back()->with('success', $request['company']);
    }

    // Updates From Modal
    public function updateROID(ROIDUpdateRequest $request, $shopid)
    {
        $lastROID = RepairOrder::where('shopid', $shopid)
            ->where('roid', $request->roid)
            ->pluck('roid')
            ->first();

        if ($lastROID) {
            return redirect()->back()->with(['error' => 'That one was already existing ROID!']);
        } else {
            RepairOrder::create([
                'roid' => $request->roid,
                'shopid' => $shopid,
                'rotype' => 'No Approval',
                'DatePromised' => now(),
                'DateInspection' => now(),
                'DateAuthorization' => now(),
                'DateParts' => now(),
                'DateIn' => '2024-08-20',
            ]);

            return redirect()->back()->with(['message' => 'Starting roid has been updated!']);
        }
    }

    public function storePartsTech(PartsTechUpdateRequest $request, $shopid)
    {
        $pid = $request->type === 'BCS' ? 'shopboss_bcs' : '';
        $pk = $request->type === 'BCS' ? '8b7d28637cd34ae3b65c88ab3122070c' : '';

        $data = [
            'shopid' => $shopid,
            'apikey' => $request->apiKey,
            'userid' => $request->userId,
        ];

        if ($request->type === 'BCS') {
            $data['partnerid'] = $pid;
            $data['partnerkey'] = $pk;
        }

        DB::table('partstechshops')->insert($data);

        return redirect()->back()->with(['message' => 'Parts tech api key integrated!']);
    }

    public function deletePartsTech($id)
    {
        DB::table('partstechshops')
            ->where('id', $id)
            ->delete();

        return redirect()->back()->with(['message' => 'Parts tech api integration deleted!']);
    }

    public function updateLinkedShops(LinkedShopsUpdateRequest $request, $shopid)
    {
        $this->shopid = $shopid;
        $joinedShops = $this->getJoinedShops();
        $newJoinedShops = $request->shops;

        // Insert
        foreach ($newJoinedShops as $newJoinedShop) {
            if (! in_array($newJoinedShop, $joinedShops)) {
                DB::table('joinedshops')->insert([
                    'shopid' => $shopid,
                    'joinedshopid' => $newJoinedShop,
                ]);
            }
        }

        // Delete
        foreach ($joinedShops as $joinedShop) {
            if (! in_array($joinedShop, $newJoinedShops)) {
                DB::table('joinedshops')
                    ->where('shopid', $shopid)
                    ->where('joinedshopid', $joinedShop)
                    ->delete();
            }
        }

        return redirect()->back()->with(['message' => 'Linked Shops updated!']);
    }

    public function updateBossBoardAccess(BossBoardAccessUpdateRequest $request, $shopid)
    {
        Company::find($shopid)->update([
            'dashexpiration' => $request->trialDate,
        ]);

        return redirect()->back()->with(['message' => 'Boss Board Expiration Date updated!']);
    }

    public function updateDVI(DVIUpdateRequest $request, $shopid)
    {
        $this->shopid = $shopid;

        $currentCompanySubscriptions = $this->getCompanyAdds();
        $requestedCompanySubscriptions = $request->subscription;

        $removeCompanySubscriptions = array_diff($currentCompanySubscriptions, $requestedCompanySubscriptions);
        $addCompanySubscriptions = array_diff($requestedCompanySubscriptions, $currentCompanySubscriptions);

        foreach ($removeCompanySubscriptions as $subscription) {
            $event = strtoupper($subscription).' Removed';
            $this->insertIntoAudit('Account Update', $event);

            DB::table('companyadds')
                ->where('shopid', $shopid)
                ->where('name', $subscription)
                ->delete();
        }

        foreach ($addCompanySubscriptions as $subscription) {
            $event = strtoupper($subscription).' Added';
            $this->insertIntoAudit('Account Update', $event);

            $price = $subscription == 'Boss Inspect' ? 149 : 30;

            DB::table('companyadds')->insert([
                'shopid' => $shopid,
                'price' => $price,
                'name' => $subscription,
            ]);
        }

        return redirect()->back()->with(['message' => 'DVI Access updated!']);
    }

    public function updateReadonly(ReadonlyUpdateRequest $request, $shopid)
    {
        if ($request->now) {
            $company = Company::find($shopid);
            $company->status = 'SUSPENDED';
            $company->datesuspended = Carbon::now();
            $company->whochurned = Auth::user()->email;
            $company->save();

            DB::table('companyadds')
                ->where('shopid', $shopid)
                ->delete();

            DB::table('futureactions')
                ->where('shopid', $shopid)
                ->where('type', 'suspend')
                ->where('status', 'active')
                ->delete();
        } else {
            DB::table('futureactions')->updateOrInsert(
                [
                    'shopid' => $shopid,
                    'type' => 'readonly',
                    'status' => 'active',
                ],
                [
                    'action_on' => $request->future,
                ]
            );
        }

        return redirect()->back()->with(['message' => 'Shop status updated!']);
    }

    public function updateForceLogOut(Request $request, $shopid)
    {
        $status = Company::find($shopid)->value('status');

        if (strtoupper($status) == 'ACTIVE') {
            $data = new ReadonlyUpdateRequest(['now' => true]);
            $this->updateReadonly($data, $shopid);
        }

        Employees::where('shopid', $shopid)
            ->update(['forcelogout' => '1']);

        return redirect()->back()->with(['message' => 'Force Log Out Completed!']);
    }

    public function updateTrialExpiration(TrialExpirationUpdateRequest $request, $shopid)
    {
        Company::find($shopid)->update([
            'trialexpiration' => $request->date,
        ]);

        $this->storeCompanyComment('Trial expiration date updated to '.Carbon::parse($request->date)->format('m/d/Y'));

        return redirect()->back()->with(['message' => 'Trial Expiration Date Updated!']);
    }

    public function updateChurn(ChurnUpdateRequest $request, $shopid)
    {
        if ($request->now) {
            $company = Company::find($shopid);

            if ($company) {
                $company->churn = 'no';
                $company->churndate = '0000-00-00';
                $company->whochurned = Auth::user()->email;
                $company->status = 'ACTIVE';
                $company->datesuspended = '0000-00-00';
                $company->save();
            }
        } elseif ($request->date) {
            $updated = DB::table('futureactions')
                ->where('shopid', $shopid)
                ->where('type', 'churn')
                ->where('status', 'active')
                ->update(['action_on' => $request->date]);

            if ($updated === 0) {
                DB::table('futureactions')->insert([
                    'shopid' => $shopid,
                    'type' => 'churn',
                    'status' => 'active',
                    'action_on' => $request->date,
                ]);
            }
        }

        return redirect()->back()->with(['message' => 'Churn And Res Code Updated!']);
    }

    public function updateMatcoToggle($shopid)
    {
        $this->shopid = $shopid;

        $company = Company::find($shopid);
        $matco = $company->matco == 'yes';

        $status = $matco ? 'no' : 'yes';
        $event = 'Account changed to '.($matco ? 'ShopBoss' : 'Matco');

        $company->matco = $status;
        $company->save();

        $this->insertIntoAudit('Account Update', $event);

        return redirect()->back()->with(['message' => "$event"]);
    }

    public function updateSchoolToggle($shopid)
    {
        $company = Company::find($shopid);

        if (! $company) {
            return redirect()->back()->with(['error' => 'Company not found.']);
        }

        $school = $company->school == 'yes';

        $status = $school ? 'no' : 'yes';
        $event = 'Account school turned '.($school ? 'off' : 'on');

        $this->shopid = $shopid;

        $company->school = $status;
        $company->save();

        $this->insertIntoAudit('Account Update', $event);

        return redirect()->back()->with(['message' => $event]);
    }

    public function updateTPMSCredentials(TPMSCredsUpdateRequest $request, $shopid)
    {
        $validatedData = $request->validated();

        $updated = DB::table('tpmsaccounts')
            ->where('shopid', $shopid)
            ->limit(1)
            ->update($validatedData);

        $message = $updated ? 'TPMS Shop Credentials Successfully Updated!' : 'Nothing to update!';

        return redirect()->back()->with('message', $message);
    }

    public function storeEpicor(EpicorUpdateRequest $request, $shopid)
    {
        DB::table('epicor')->insert([
            'shopid' => $shopid,
            'suppliername' => $request->supplier,
            'programgroup' => $request->group,
            'partnercompany' => $request->supplier,
            'storeid' => $request->sellerID,
            'username' => $request->username,
            'password' => $request->password,
            'addedby' => 'SBP',
            'idlink' => 'Integrated Parts Ordering & Labor Times',
        ]);

        return redirect()->back()->with('message', 'The new Epicor user has been added successfully!');
    }

    public function deleteEpicor($id)
    {
        DB::table('epicor')->where('id', $id)->delete();

        return redirect()->back()->with('message', 'The Epicor user has been deleted successfully!');
    }

    public function updatePayments360(Payments360UpdateRequest $request, $shopid)
    {
        $company = Company::find($shopid);
        $company->merchantaccount = $request->type;
        $company->terminalid = $request->terminalID;
        $company->paytoken = $request->token;
        $company->cfpid = $request->cfpid;
        $company->save();

        return redirect()->back()->with('message', '360 Payment has been updated successfully!');
    }

    public function updateCardknoxKeys(CardknoxKeysUpdateRequest $request, $shopid)
    {
        $company = Company::find($shopid);
        $company->merchantid = $request->cardknoxKey;
        $company->merchantpassword = $request->ifieldsKey;
        $company->save();

        return redirect()->back()->with('message', 'Cardknox Payment Keys has been updated successfully!');
    }

    public function updateCardknoxActivate($shopid)
    {
        $company = Company::find($shopid);
        $company->merchantaccount = 'cardknox';
        $company->save();

        return redirect()->back()->with('message', 'Cardknox Payment has been updated successfully!');
    }

    public function updateCardknoxTerminal(CardknoxTerminalUpdateRequest $request, $shopid)
    {
        DB::table('cipdevices')->insert([
            'shopid' => $shopid,
            'hostip' => $request->ip,
            'label' => $request->name,
        ]);

        return redirect()->back()->with('message', 'Cardknox Payment Terminal been updated successfully!');
    }

    public function deleteCardknoxTerminal($id)
    {
        DB::table('cipdevices')->where('id', $id)->delete();

        return redirect()->back()->with('message', 'Cardknox Payment Terminal been deleted successfully!');
    }

    public function updatePackage(ShopPackageUpdateRequest $request, $shopid)
    {
        $company = Company::find($shopid);
        if (! str_contains($request->package, 'trial') && ! $request->future) {
            $company->newpackagetype = $request->package;
            $company->flatprice = $this->getFlatPrice($request->package);
            $company->save();

            DB::table('packagetrials')->where('shopid', $shopid)->delete();

        } elseif (str_contains($request->package, 'trial')) {
            $company->newpackagetype = $request->package;
            $company->flatprice = $this->getFlatPrice($request->package);
            $company->save();

            $packagetrials = DB::table('packagetrials')
                ->where('shopid', $shopid)
                ->first();

            $oldplan = $packagetrials ? $packagetrials->newplan : $request->package;

            DB::table('packagetrials')->updateOrInsert(
                ['shopid' => $shopid],
                [
                    'oldplan' => $oldplan,
                    'newplan' => $request->package,
                    'expiration' => $request->future,
                ]
            );
        } elseif ($request->future) {
            $packagetrials = DB::table('packagetrials')
                ->where('shopid', $shopid)
                ->first();

            $oldplan = $packagetrials ? $packagetrials->newplan : $request->package;

            DB::table('packagetrials')->updateOrInsert(
                ['shopid' => $shopid],
                [
                    'oldplan' => $oldplan,
                    'newplan' => $request->package,
                    'expiration' => $request->future,
                ]
            );
        }

        return redirect()->back()->with('message', 'Shop Package has been updated successfully!');
    }

    public function updateSuspendReactivate(Request $request, $shopid)
    {
        $this->shopid = $shopid;
        $company = Company::find($shopid);
        $compantStatus = $company->status;
        $userEmail = Auth::user()->email;

        if ($request->now) {
            if ($compantStatus == 'ACTIVE') {
                $company->status = 'SUSPENDED';
                $company->whochurned = $userEmail;
                $company->datesuspended = Carbon::now();
                $company->save();

                DB::table('companyadds')->where('shopid', $shopid)->delete();
                DB::table('futureactions')->where('shopid', $shopid)->where('type', 'suspend')->where('status', 'active')->delete();

                $response = Http::get('https://hooks.zapier.com/hooks/catch/2237757/o877o9g/', [
                    'shopid' => $shopid,
                ]);

                $this->zaphierRequest('suspended', Carbon::now());

                $this->storeCompanyComment('Shop Suspended');

                return redirect()->back()->with('message', 'Shop Suspended successfully!');
            } else {
                $company->status = 'ACTIVE';
                $company->whochurned = '';
                $company->datesuspended = '0000-00-00';
                $company->save();

                $this->zaphierRequest('active', '');

                Employees::where('shopid', $shopid)->update([
                    'forcelogout' => '0',
                ]);

                $this->storeCompanyComment('Shop Reactivated');

                return redirect()->back()->with('message', 'Shop Reactivated successfully!');
            }
        } else {
            if ($request->date) {
                DB::table('futureactions')->updateOrInsert(
                    ['shopid' => $shopid, 'type' => 'suspend', 'status' => 'active'],
                    [
                        'shopid' => $shopid,
                        'type' => 'suspend',
                        'action_on' => $request->date,
                    ]
                );
            } else {
                DB::table('futureactions')->where('shopid', $shopid)->delete();
            }

            return redirect()->back()->with('message', 'Shop future suspend date set successfully!');
        }
    }

    public function suspendShop($shopid)
    {
        return $shopid;
    }
    // END

    // AR MATIC
    public function getPayBalanceLink(Request $request, $shopid)
    {
        if ($request->expectsJson()) {
            $paylink = '';
            $cacheKey = 'paylink_'.$shopid;
            $cacheTTL = 3600;

            $cachedData = Cache::get($cacheKey);

            if ($cachedData) {
                $json = $cachedData;
            } else {
                $url = 'https://api.armatic.com/customers?query='.$shopid;

                $response = Http::withHeaders([
                    'Accept' => 'application/json',
                    'Authorization' => 'Bearer YcRuU2VlFBs25r99avyhJC1fsV25Uoia8cOJwWHshq9u_sJCCs9y0vJ1sGOjxaiJ8ljKfbgqCrqpW25Z6eCgfAPgfo8VeBE1WXg=',                    'User-Agent' => $_SERVER['HTTP_USER_AGENT'],
                ])->get($url);

                $json = $response->json();

                Cache::put($cacheKey, $json, $cacheTTL);
            }

            if (isset($json['customers'])) {
                foreach ($json['customers'] as $ashop) {
                    if ($ashop['account_number'] == $shopid) {
                        $paylink = $ashop['pay_balance_link'];
                        break;
                    }
                }
            }

            return response()->json(['payLink' => $paylink], 200);
        }
        abort(404);
    }
    // END

    public function updateBetaFeatures(Request $request, $shopid)
    {
        $requestedFeatureIds = $request->input('selectedFeatures', []);

        $existingFeatureIds = DB::table('beta_features')
            ->where('shopid', $shopid)
            ->pluck('feature_id')
            ->toArray();

        $featuresToDelete = array_diff($existingFeatureIds, $requestedFeatureIds);
        if (! empty($featuresToDelete)) {
            DB::table('beta_features')
                ->where('shopid', $shopid)
                ->whereIn('feature_id', $featuresToDelete)
                ->delete();
        }

        $featuresToInsert = array_diff($requestedFeatureIds, $existingFeatureIds);
        if (! empty($featuresToInsert)) {
            $insertData = array_map(fn ($featureId) => [
                'shopid' => $shopid,
                'feature_id' => $featureId,
            ], $featuresToInsert);

            DB::table('beta_features')->insert($insertData);
        }

        return redirect()->back()->with('message', 'Beta Features have been updated successfully!');
    }

    public function storeComment(Request $request, $shopid)
    {
        $request->validate([
            'comment' => 'required|string|min:1|max:2000',
        ]);

        $this->shopid = $shopid;
        $this->storeCompanyComment($request->comment);

        return redirect()->back()->with('message', 'New comment has been added successfully!');
    }

    public function updateOnboardingCompletedStatus(Request $request, $shopid)
    {
        $request->validate([
            'status' => 'required|in:completed,not completed',
        ]);

        $status = $request->status === 'completed';

        CustomerOnboarding::updateOrCreate(
            ['shopid' => $shopid],
            [
                'general_info' => $status,
                'settings' => $status,
                'employees' => $status,
                'suppliers' => $status,
                'customize' => $status,
            ]
        );

        return redirect()->back()->with('message', 'Onboarding wizard status updated successfully!');
    }

    public function updateOnboardingAnimationStatus(Request $request, $shopid)
    {
        $request->validate([
            'status' => 'required|in:on,off',
        ]);

        $status = $request->status === 'off';

        CustomerOnboarding::updateOrCreate(
            ['shopid' => $shopid],
            [
                'animation' => $status,
            ]
        );

        return redirect()->back()->with('message', 'Onboarding wizard animation status updated successfully!');
    }

    public function updateShopToPaid($shopid)
    {
        $company = Company::find($shopid);
        $company->package = 'Paid';
        $company->dateofacceptance = Carbon::now();
        $company->save();

        return redirect()->back()->with('message', 'Shop upgraded to paid successfully!');
    }

    // DataTable Functions
    public function commentsDatatable($shopid)
    {
        $data = CompanyComments::select([
            'id',
            'shopid',
            'ts',
            'comment',
        ])
            ->where('shopid', $shopid);

        return Datatables::of($data)
            ->make(true);
    }

    public function usersDatatable($shopid)
    {
        $data = Employees::select([
            'id',
            DB::raw("CONCAT(EmployeeFirst, ' ', EmployeeLast) as employee"),
            'mode',
            'password',
            'jobdesc',
        ])
            ->where('shopid', $shopid)
            ->where('Active', 'yes');

        return Datatables::of($data)
            ->make(true);
    }

    public function updateEmployeePassword(PasswordRequest $request)
    {
        Employees::where('id', $request->id)->update([
            'password' => $request->password,
            'passwordenc' => Hash::make($request->password),
            'forcelogout' => '1',
        ]);

        return redirect()->back()->with('message', 'Employee password has been successfully updated');
    }

    public function reportsDatatable($shopid)
    {
        $data = DB::table('customreports')
            ->select([
                'name',
                'ts',
            ])
            ->where('shopid', $shopid);

        return Datatables::of($data)
            ->make(true);
    }

    public function paymentsDatatable($shopid)
    {
        $data = DB::table('payments')
            ->select([
                'paymentdate',
                'tracenumber',
                'amount',
                'approvalcode',
            ])
            ->where('shopid', $shopid);

        return Datatables::of($data)
            ->editColumn('amount', fn ($row) => $this->asDollars($row->amount))
            ->make(true);
    }

    public function invoicesDatatable($shopid)
    {
        $data = DB::table('invoices')
            ->select([
                'id',
                'invoicedate',
                'amt',
            ])
            ->where('shopid', $shopid);

        return Datatables::of($data)
            ->editColumn('amt', fn ($row) => $this->asDollars($row->amt))
            ->make(true);
    }

    public function gainersOrLoosers(Request $request)
    {
        if ($request->json()) {
            $request->validate([
                'gainers' => 'required',
            ]);

            $userRole = Auth::user()->role == 'matco';

            // 3 Month Date Range
            $endDate = (new DateTime)->format('Y-m-d');
            $startDate = (new DateTime)->sub(new DateInterval('P3M'))->format('Y-m-d');

            // Cache key
            $cacheKey = 'gainersOrLoosers_'.($request->input('gainers') === 'true' ? 'gainers' : 'loosers').$userRole."_{$startDate}_{$endDate}";

            $data = Cache::remember($cacheKey, now()->addMinutes(60), function () use ($startDate, $endDate, $request) {
                $subquery = DB::table('repairorders')
                    ->select('shopid',
                        DB::raw('SUM(totalro) AS totalRO'),
                        DB::raw('COUNT(*) AS count'))
                    ->where('status', 'closed')
                    ->where('rotype', '!=', 'No Approval')
                    ->whereBetween('StatusDate', [$startDate, $endDate])
                    ->groupBy('shopid');

                return DB::table(DB::raw("({$subquery->toSql()}) as aggregated"))
                    ->mergeBindings($subquery)
                    ->join('company', 'aggregated.shopid', '=', 'company.shopid')
                    ->select(
                        'aggregated.shopid',
                        'aggregated.totalRO',
                        'aggregated.count',
                        'company.companyName',
                        'company.companyPhone',
                        'company.newpackagetype',
                    )
                    ->when($request->input('gainers') === 'true', function ($query) {
                        return $query->orderBy('aggregated.totalRO', 'desc');
                    }, function ($query) {
                        return $query->orderBy('aggregated.totalRO', 'asc');
                    })
                    ->when(Auth::user()->role == 'matco', function ($query) {
                        return $query->where('company.matco', 'yes');
                    })
                    ->limit(10)
                    ->get()
                    ->map(function ($company) {
                        $company->companyName = html_entity_decode($company->companyName);

                        return $company;
                    });
            });

            return response()->json($data, 200);
        }
        abort(404);
    }

    public function newOrOldCompanies(NewOrOldCompaniesRequest $request)
    {
        if ($request->json()) {
            $userRole = Auth::user()->role == 'matco';

            // Cache key
            $cacheKey = 'newOrOldCompanies_'.($request->input('new') === 'true' ? 'new' : 'old').$userRole;

            // Caching logic
            $data = Cache::remember($cacheKey, now()->addMinutes(60), function () use ($request) {
                return Company::select([
                    'companyName',
                    'companyPhone',
                    'shopid',
                    'newpackagetype',
                    'dateStarted',
                ])
                    ->whereRaw('lcase(status) != ?', ['suspended'])
                    ->where('shopid', '!=', 'demo')
                    ->where('package', 'Paid')
                    ->when($request->input('new') == 'true', function ($query) {
                        return $query->orderBy('ts', 'desc');
                    }, function ($query) {
                        return $query->orderBy('ts', 'asc');
                    })
                    ->when(Auth::user()->role == 'matco', function ($query) {
                        return $query->where('matco', 'yes');
                    })
                    ->limit(10)
                    ->get()
                    ->map(function ($company) {
                        $company->companyName = html_entity_decode($company->companyName);

                        return $company;
                    });
            });

            return response()->json($data, 200);
        }
        abort(404);
    }
    // END

    // BACKOFFICE API
    public function requestCardknoxKeys($shopid)
    {
        // Get company details
        $company = DB::table('backofficekeys')
            ->where('shopid', $shopid)->first();

        if (! $company) {
            return 'Company not found.';
        }

        // Construct the API URL
        $shopbossSite = env('SHOPBOSS_SITE');
        $subdomain = stripos($shopbossSite, 'staging') !== false ? 'sandbox' : '';
        $url = 'https://api'.$subdomain.'.backofficellcapi.com/datafeed/v5/partners/ff8f6d5a-2ac2-4ffd-aff4-0a637338acac/accountinglinksubscription/order';

        // Prepare data for the API request
        $data = [
            'AccountId' => $shopid,
            'AccountName' => $company->companyname,
            'AccountPhone' => $company->companyphone,
            'AccountAddressLine1' => $company->companyaddress,
            'AccountAddressLine2' => '',
            'AccountAddressCity' => $company->companycity,
            'AccountAddressStateProvince' => $company->companystate,
            'AccountAddressZip' => $company->companyzip,
            'ContactFirstName' => $company->contactfname,
            'ContactLastName' => $company->contactlname,
            'ContactEmail' => $company->contactemail,
            'ContactPhone' => $company->contactphone,
            'SalesRepFirstName' => 'MATT',
            'SalesRepLastName' => 'BOSHAW',
            'SalesRepEmail' => '<EMAIL>',
            'SalesRepPhone' => '**********',
            'OrderNotes' => '',
        ];

        // Encode the data into JSON
        $jsonEncodedData = json_encode($data);

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonEncodedData);
        curl_setopt($ch, CURLOPT_USERPWD, '0018A001022UGyoQAZ:h>MloPe<7s!D.,s4!@37!)2_iM-6>k-PME');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: '.strlen($jsonEncodedData),
        ]);

        // Execute the cURL request and get the response
        $response = curl_exec($ch);

        // Check if cURL encountered an error
        if (curl_errno($ch)) {
            return response()->json(['error' => 'API request failed: '.curl_error($ch)], 500);
        }

        // Close the cURL resource
        curl_close($ch);

        // Process the API response
        $result = str_replace('"', '', $response);

        if (strlen($result) <= 40 && stripos($result, '-') !== false) {
            // Update backofficekeys table
            DB::table('backofficekeys')
                ->where('shopid', $shopid)
                ->update(['orderid' => $result, 'active' => 'yes']);

            // Insert comment into companycomments table
            $comment = 'Back Office Integration request sent';

            $this->shopid = $shopid;
            $this->storeCompanyComment($comment);
        }

        return redirect()->back()->with('message', 'Request to Back Office set successfully!');
    }
    // END

    public function getSMSNumbers(Request $request)
    {
        $request->validate([
            'ac' => 'required|string',
        ]);

        $cityar = explode('|', $request->ac);
        $city = str_replace(' ', '+', $cityar[0]);
        $state = $cityar[1];

        $username = 'c88edfa0-fc8e-4d1d-92ea-dfd6256f6281';
        $password = 'ej488O241AT8Eh6';
        $acctid = '5005858';

        // API Endpoints to check
        $urls = [
            "https://dashboard.bandwidth.com/api/accounts/$acctid/availableNumbers?city=$city&state=$state&quantity=7",
            "https://dashboard.bandwidth.com/api/accounts/$acctid/availableNumbers?state=$state&quantity=7",
            "https://dashboard.bandwidth.com/api/accounts/$acctid/availableNumbers?state=MA&quantity=7",
        ];

        foreach ($urls as $url) {
            $response = Http::withBasicAuth($username, $password)->get($url);

            if ($response->successful() && ! empty($response->body())) {
                try {
                    $xml = new SimpleXMLElement($response->body());
                    if (! empty($xml->TelephoneNumberList->TelephoneNumber)) {
                        $numbers = json_decode(json_encode($xml->TelephoneNumberList->TelephoneNumber), true);

                        return response()->json(['status' => true, 'availableNumbers' => $numbers, 'URL' => $url]);
                    }
                } catch (\Exception $e) {
                    return response()->json(['status' => false, 'error' => 'XML Parsing Error: '.$e->getMessage()]);
                }
            }
        }

        return response()->json(['status' => false, 'error' => 'No available numbers found.']);
    }

    public function deleteSMSNumber($shopid)
    {
        $data = DB::table('smsnumbers')->where('shopid', $shopid)->first(['smsnum', 'orderid']);
        $number = $data->smsnum;
        $orderid = $data->orderid;

        DB::table('smsnumbers')->where('shopid', $shopid)->delete();

        if ($orderid) {
            $xml = '<?xml version="1.0"?>
            <DisconnectTelephoneNumberOrder>
                <CustomerOrderId>'.$orderid.'</CustomerOrderId>
                <DisconnectTelephoneNumberOrderType>
                    <TelephoneNumberList>
                        <TelephoneNumber>'.$number.'</TelephoneNumber>
                    </TelephoneNumberList>
                </DisconnectTelephoneNumberOrderType>
            </DisconnectTelephoneNumberOrder>';

            // API credentials
            $username = 'c88edfa0-fc8e-4d1d-92ea-dfd6256f6281';
            $password = 'ej488O241AT8Eh6';
            $acctid = '5005858';

            // API endpoint
            $url = "https://dashboard.bandwidth.com/api/accounts/$acctid/disconnects";

            // Initialize cURL
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/xml; charset=utf-8']);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_USERPWD, "$username:$password");

            curl_exec($ch);

            if (curl_errno($ch)) {
                curl_close($ch);

                return redirect()->back()->with('error', 'API request failed: '.curl_error($ch));
            }

            curl_close($ch);
        }

        $this->storeCompanyComment('SMS Number '.$number.' disconnected');

        return redirect()->back()->with('message', 'SMS Number successfully deleted!');
    }

    public function saveSMSNumber(Request $request)
    {
        $request->validate([
            'shopid' => 'required|string',
            'selectedNumber' => 'required|string',
        ]);

        $this->saveSMSNumberAction($request->shopid, $request->selectedNumber);

        return redirect()->back()->with('message', 'SMS Number successfully added!');
    }

    private function saveSMSNumberAction($shopid, $number)
    {
        $xml = '<Order>'.
        "<Name>$shopid</Name>".
        '<ExistingTelephoneNumberOrderType>'.
         '<TelephoneNumberList>'.
          "<TelephoneNumber>$number</TelephoneNumber>".
         '</TelephoneNumberList>'.
        '</ExistingTelephoneNumberOrderType>'.
        '<SiteId>30273</SiteId>'.
        '</Order>';

        $username = 'c88edfa0-fc8e-4d1d-92ea-dfd6256f6281';
        $password = 'ej488O241AT8Eh6';

        $acctid = '5005858';
        $url = "https://dashboard.bandwidth.com/api/accounts/$acctid/orders";
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/xml']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $xml);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_USERPWD, "$username".':'."$password");

        $result = curl_exec($ch);
        $response = json_decode(json_encode(simplexml_load_string($result)));
        curl_close($ch);

        DB::table('smsnumbers')->insert([
            'shopid' => $shopid,
            'smsnum' => $number,
            'orderid' => $response->Order->id,
        ]);
    }
}
