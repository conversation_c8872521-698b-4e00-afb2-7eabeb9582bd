<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShopUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'company.profitboost' => 'required|string',
            'company.onboardinghours' => ['required', 'numeric', 'regex:/^\d+(\.\d{1,4})?$/'],
            'company.newpackagetype' => 'required|string|max:255',
            'company.status' => 'required|string',
            'company.carfaxlocation' => 'required',
            'company.merchantaccount' => 'required|string|max:255',

            'settings.motor' => 'required|string|in:yes,no',
            'settings.failedpayment' => 'required|string|in:yes,no',
            'settings.pph' => 'required|string|in:yes,no',
            'settings.oss' => 'required|string|in:yes,no',

            'dataTransfer.type' => 'nullable|string',
            'dataTransfer.status' => 'nullable|string',

            'backofficeKeys.active' => 'required|string|in:yes,no',
            'comment' => 'nullable|string|min:1|max:2000',
        ];
    }

    public function messages()
    {
        return [
            'company.contact.string' => 'The contact must be a string.',
            'company.contact.max' => 'The contact may not be greater than 255 characters.',
            'company.profitboost.required' => 'The profitboost field is required.',
            'company.profitboost.string' => 'The profitboost must be a string.',
            'company.onboardinghours.required' => 'The onboarding hours field is required.',
            'company.onboardinghours.numeric' => 'The onboarding hours must be a number.',
            'company.onboardinghours.regex' => 'The onboarding hours format is invalid.',
            'company.newpackagetype.required' => 'The new package type field is required.',
            'company.newpackagetype.string' => 'The new package type must be a string.',
            'company.newpackagetype.max' => 'The new package type may not be greater than 255 characters.',
            'company.status.required' => 'The status field is required.',
            'company.status.string' => 'The status must be a string.',
            'company.carfaxlocation.required' => 'The Carfax location field is required.',
            'company.merchantaccount.required' => 'The merchant account field is required.',
            'company.merchantaccount.string' => 'The merchant account must be a string.',
            'company.merchantaccount.max' => 'The merchant account may not be greater than 255 characters.',
            'settings.motor.required' => 'The motor field is required.',
            'settings.motor.string' => 'The motor must be a string.',
            'settings.motor.in' => 'The selected motor is invalid.',
            'settings.failedpayment.required' => 'The failed payment field is required.',
            'settings.failedpayment.string' => 'The failed payment must be a string.',
            'settings.failedpayment.in' => 'The selected failed payment is invalid.',
            'settings.pph.required' => 'The PPH field is required.',
            'settings.pph.string' => 'The PPH must be a string.',
            'settings.pph.in' => 'The selected PPH is invalid.',
            'dataTransfer.type.string' => 'The data transfer type must be a string.',
            'dataTransfer.status.string' => 'The data transfer status must be a string.',
            'backofficeKeys.required' => 'The backoffice keys field is required.',
            'backofficeKeys.string' => 'The backoffice keys must be a string.',
            'backofficeKeys.in' => 'The selected backoffice keys is invalid.',
        ];
    }
}
