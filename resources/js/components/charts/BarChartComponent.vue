<template>
    <div class="card radius-10 overflow-hidden">
        <div class="card-body">
            <div class="d-flex align-items-center mb-2">
                <div class="">
                    <p class="mb-1 text-secondary">{{ title }}</p>
                    <h4 class="mb-0">{{ subtitle }}</h4>
                </div>
                <div class="ms-auto">
                    <p :class="['mb-1', 'font-11', increase > 0 ? 'text-success' : 'text-danger']" style="text-align: right;">
                        {{ (increase) }} {{ increase > 0 ? 'Increase' : 'Decrease' }}
                    </p>
                    <p class="mb-0 font-11 text-success" style="text-align: right;">{{ (total) }} Total</p>
                </div>
            </div>
            <VueApexCharts width="100%" height="100" type="line" :options="chartOptions" :series="series"></VueApexCharts>
        </div>
    </div>
</template>

<script setup>
    import { ref, watch } from 'vue';
    import VueApexCharts from 'vue3-apexcharts';

    const props = defineProps({
        data: Array,
        title: String,
        subtitle: String,
        total: Number,
        increase: Number
    });

    const series = ref([
        {
            name: props.subtitle,
            data: props.data
        }
    ]);

    const chartOptions = ref({
        chart: {
            type: 'area',
            height: 100,
            toolbar: {
                show: false
            },
            zoom: {
                enabled: false
            },
            dropShadow: {
                enabled: false,
                top: 3,
                left: 14,
                blur: 4,
                opacity: 0.12,
                color: '#1E90FF'
            },
            sparkline: {
                enabled: true
            }
        },
        markers: {
            size: 0,
            colors: ['#1E90FF'],
            strokeColors: '#fff',
            strokeWidth: 2,
            hover: {
                size: 7
            }
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            show: true,
            width: 2,
            curve: 'smooth'
        },
        colors: ['#1E90FF'],
        fill: {
            opacity: 1
        },
        tooltip: {
            theme: 'dark',
            fixed: {
                enabled: false
            },
            x: {
                show: false
            },
            y: {
                title: {
                    formatter: function () {
                        return ''
                    }
                }
            },
            marker: {
                show: false
            }
        }
    });


    watch(
        () => props.data,
        (newData) => {
            series.value = [
                {
                    name: props.subtitle,
                    data: newData
                }
            ];
        }
    );
</script>
