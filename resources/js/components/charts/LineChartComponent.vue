<template>
    <div class="card radius-10 overflow-hidden">
        <div class="card-body">
            <div v-if="loading" class="d-flex align-items-center justify-content-center" style="height: 450px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div v-else>
                <div class="d-flex align-items-center mb-2">
                    <div class="">
                        <p class="mb-1 text-secondary">{{ title }}</p>
                        <h4 class="mb-0">{{ subtitle }}</h4>
                    </div>
                    <div class="ms-auto">
                        <p :class="['mb-1', 'font-11', increase > 0 ? 'text-success' : 'text-danger']" style="text-align: right;">
                            {{ asDollars(increase) }} {{ increase > 0 ? 'Increase' : 'Decrease' }}
                        </p>
                        <p class="mb-0 font-11 text-success" style="text-align: right;">{{ asDollars(total) }} Total</p>
                    </div>
                </div>
                <VueApexCharts width="100%" height="400" type="line" :options="chartOptions" :series="series"></VueApexCharts>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, defineComponent, watch } from 'vue';
import VueApexCharts from 'vue3-apexcharts';
import { asDollars } from '@/utils/utils.js'

const chart_data = defineProps({
    data: Array,
    labels: Array,
    title: String,
    subtitle: String,
    total: Number,
    increase: Number,
    loading: {
        type: Boolean,
        default: false
    }
})

defineComponent({
    components: {
        apexchart: VueApexCharts,
    },
});

const series = ref([
    {
        name: 'Price',
        data: chart_data.data,
    },
]);

const chartOptions = ref({
    chart: {
        type: "line",
        height: "400px",
        width: "100%",
        foreColor: "#9ba7b2",
        zoom: {
            enabled: false
        },
        dropShadow: {
            enabled: true,
            top: 3,
            left: 14,
            blur: 4,
            opacity: 0.1
        },
        toolbar: {
            show: false
        }
    },
    stroke: {
        curve: 'smooth',
        width: 2,
    },
    markers: {
        size: 0,
        colors: ['#9ba7b2'],
        strokeColor: '#fff',
        strokeWidth: 2,
        hover: {
            size: 7,
        }
    },
    grid: {
        borderColor: '#e7e7e7',
        row: {
            colors: ['#f3f3f3', 'transparent'],
            opacity: 0.5
        },
    },
    xaxis: {
        categories: chart_data.labels,
        title: {
            text: 'Date',
            style: {
                // color: 'grey',
                fontWeight: 'bold'
            }
        },
        labels: {
            style: {
                // colors: 'grey',
                fontSize: '12px'
            }
        }
    },
    yaxis: {
        title: {
            text: 'Price',
            style: {
                // color: '#333',
                fontWeight: 'bold'
            }
        },
        labels: {
            style: {
                // colors: '#333',
                fontSize: '12px'
            },
            formatter: function (value) {
                return value.toLocaleString("en-US", { style: "currency", currency: "USD" });
            }
        }
    },
    tooltip: {
        theme: 'dark',
        x: {
            format: 'dd MMM'
        },
    }
});


watch(chart_data, (newVal) => {
    series.value = [
        {
            name: 'Price',
            data: newVal.data,
        },
    ];
    chartOptions.value = {
        ...chartOptions.value,
        xaxis: {
            categories: newVal.labels,
        }
    };
}, { deep: true });
</script>
