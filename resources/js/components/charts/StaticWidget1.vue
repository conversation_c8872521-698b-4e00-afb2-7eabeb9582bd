<template>
    <div class="card radius-10">
        <div class="card-body">
            <div v-if="loading" class="d-flex align-items-center justify-content-center" style="height: 100px;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div v-else class="d-flex align-items-center">
                <div>
                    <p class="mb-0 text-secondary">{{ title }}</p>
                    <h4 class="my-1">{{ formattedTotal }}</h4>
                    <p :class="['mb-0', 'font-13', textColor]">
                        <i :class="['bx', arrow, 'align-middle']"></i>
                        {{ formattedIncrease }}
                    </p>
                </div>
                <div :class="['widgets-icons', 'ms-auto', svg]">
                    <i :class='boxIcon'></i>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import { asDollars } from '@/utils/utils.js'

    const props = defineProps({
        total: Number,
        title: String,
        increase: Number,
        dollarFormat: Boolean,
        loading: {
            type: Boolean,
            default: false
        }
    });

    const formattedIncrease = computed(() => props.dollarFormat ? asDollars(props.increase) : props.increase);
    const formattedTotal = computed(() => props.dollarFormat ? asDollars(props.total) : props.total);

    const textColor = computed(() => props.increase > 0 ? 'text-success' : 'text-danger');
    const arrow = computed(() => props.increase > 0 ? 'bxs-up-arrow' : 'bxs-down-arrow');
    const svg = computed(() => props.increase > 0 ? 'bg-light-success text-success' : 'bg-light-danger text-danger');
    const boxIcon = computed(() => props.increase > 0 ? 'bx bx-line-chart' : 'bx bx-line-chart-down');
</script>
