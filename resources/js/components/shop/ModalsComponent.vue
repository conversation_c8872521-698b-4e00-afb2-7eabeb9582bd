<template>
    <form @submit.prevent="startingRepairOrderNumber.put(route('shop.update.roid', { shopid: shopid }), {
            onSuccess: () => shopStore.setState('changeStartingRONumber')
        })">
        <ModalComponent v-model="shopStore.state.changeStartingRONumber" :title="'Change Starting RO Number'">
            <template #body>
                <InputComponent
                    label="Repair Order ID"
                    placeholder="Enter ROID"
                    iconClass="bx bx-barcode"
                    v-model="startingRepairOrderNumber.roid"
                    :error="startingRepairOrderNumber.errors.roid"
                    :required="true"
                />
            </template>

            <template #footer>
                <ButtonComponent :processing="startingRepairOrderNumber.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="startingRepairOrderNumber.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'ROID Update'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="partsTech.post(route('shop.store.partstech', {'shopid': shopid}), {
        preserveState: (page) => Object.keys(page.props.errors).length > 0,
        onSuccess: () => {
            shopStore.setState('partsTech'),
            shopStore.setState('partsTechWasSuccessful')
        }
    })">
        <ModalComponent v-model="shopStore.state.partsTech" :title="'Add Parts Tech'" size="big">
            <template #body>
                <InputComponent
                    label="User ID"
                    iconClass="bx bx-user"
                    v-model="partsTech.userId"
                    placeholder="User ID"
                    :error="partsTech.errors.userId"
                    :required="true"
                />

                <InputComponent
                    label="API Key"
                    v-model="partsTech.apiKey"
                    placeholder="API Key"
                    iconClass="bx bx-key"
                    :error="partsTech.errors.apiKey"
                    :required="true"
                />

                <Select2Component
                    :data="['Standard', 'BCS']"
                    v-model="partsTech.type"
                    label="Type"
                    iconClass="bx bx-category"
                    :error="partsTech.errors.type"
                    :required="true"
                />

                <div class="partsTech" v-if="props.partTech.length">
                    <hr>
                    <div class="header">
                        <p>Id</p>
                        <p>User Id</p>
                        <p>API Key</p>
                        <p>Action</p>
                    </div>
                    <div class="data">
                        <div class="item" v-for="partTech in props.partTech">
                            <p>{{partTech.id}}</p>
                            <p>{{partTech.userid}}</p>
                            <p>{{partTech.apikey}}</p>
                            <button type="button" class="btn btn-danger" @click="deletePartsTechSupplier(partTech.id, partTech.userid)">Delete</button>
                        </div>
                    </div>
                    <hr>
                </div>
            </template>

            <template #footer>
                <ButtonComponent :processing="partsTech.processing" name="Add"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.partsTechWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Parts Tech'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="deletePartsTech.delete(route('shop.delete.partstech', {'id': deletePartsTech.id}),
        {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('partsTech'),
                shopStore.setState('deletePartsTech'),
                shopStore.setState('partsTechDeleteWasSuccessful')
            }
        })">
        <ModalComponent v-model="shopStore.state.deletePartsTech" title="Delete User" :visible="deletePartsTech.visible">
            <template #body>
                <p>Are you sure you want to delete parts tech supplier {{deletePartsTech.name}}?</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="deletePartsTech.processing" name="Delete" class="danger"/>
            </template>
        </ModalComponent>
        <BaseAlertComponent v-if="shopStore.state.partsTechDeleteWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Parts Tech'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>


    <form @submit.prevent="linkedShops.put(route('shop.update.linked-shops', {'shopid': shopid}), {
            onSuccess: () => {
                shopStore.setState('joinedShops')
            }
        })">
        <ModalComponent v-model="shopStore.state.joinedShops" title="Linked Shops">
            <template #body>
                <Select2Component
                    :data="props.companies"
                    :multiple="true"
                    :keyValue="true"
                    v-model="linkedShops.shops"
                    label="Linked Shops"
                    iconClass="bx bx-store"
                    :error="linkedShops.errors.shops"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="linkedShops.processing" class="primary"/>
            </template>
        </ModalComponent>
        <BaseAlertComponent v-if="linkedShops.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Linked Shops'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="readOnly.put(route('shop.update.readonly', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('readOnly'),
                shopStore.setState('readOnlyWasSuccessful')
            }
        })">
        <ModalComponent v-model="shopStore.state.readOnly" title="Update Shop Access Read Only">
            <template #body>
                <InputComponent
                    v-model="readOnly.future"
                    label="Read Only Date"
                    type="date"
                    iconClass="bx bx-calendar"
                    placeholder="Read Only Date"
                    :error="readOnly.errors.future"
                    :disabled="readOnly.now"
                />

                <CustomInputComponent label="Read Only Now" inputId="readOnlyNow" iconClass="bx bx-user" :error="readOnly.errors.now">
                    <template #input>
                        <span class="input-group-text">
                            <input class="form-check-input" type="checkbox" role="switch" v-model="readOnly.now" name="readOnlyNow" id="readOnlyNow">
                        </span>
                        <span class="input-group-text">
                            <label class="form-check-label" for="readOnlyNow">
                                Read Only Now
                            </label>
                        </span>
                    </template>
                </CustomInputComponent>
            </template>
            <template #footer>
                <ButtonComponent :processing="readOnly.processing" class="primary"/>
            </template>
        </ModalComponent>
        <BaseAlertComponent v-if="shopStore.state.readOnlyWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            title="Read Only"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form
        @submit.prevent="shopPackage.put(route('shop.update.package', {'shopid': shopid}),{
                preserveState: (page) => Object.keys(page.props.errors).length > 0,
                onSuccess: () => {
                    shopStore.setState('changeShopPackage'),
                    shopStore.setState('changeShopPackageWasSuccessful')
                }
            })">
        <ModalComponent v-model="shopStore.state.changeShopPackage" title="Change Shop Package">
            <template #body>
                <Select2Component
                    :data="['silver','gold', 'platinum', 'premier', 'premier plus', 'gold trial', 'platinum trial']"
                    v-model="shopPackage.package"
                    label="Package"
                    iconClass="bx bx-box"
                    :error="shopPackage.errors.package"
                />

                <CustomInputComponent label="Trial Date" iconClass="bx bx-calendar" :error="shopPackage.errors.trialExpiration" v-if="shopPackage.package.includes('trial')">
                    <template #input>
                        <span class="input-group-text">
                            <input class="form-check-input" v-model="shopPackage.trialOn" type="checkbox" role="switch" id="flexSwitchCheckDefault1">
                        </span>
                        <input type="date" v-model="shopPackage.trialExpiration" class="form-control" placeholder="Trial date" :disabled="shopPackage.trialOn" name="trialDate" id="trialDate">
                    </template>
                </CustomInputComponent>

                <CustomInputComponent label="Future Date" inputId="flexSwitchCheckDefault1" iconClass="bx bx-calendar" :error="shopPackage.errors.future">
                    <template #input>
                        <span class="input-group-text">
                            <input class="form-check-input" v-model="shopPackage.futureOn" type="checkbox" role="switch" id="flexSwitchCheckDefault1">
                        </span>
                        <input type="date" v-model="shopPackage.future" class="form-control" placeholder="Future date" :disabled="shopPackage.futureOn" name="futureDate" id="futureDate">
                    </template>
                </CustomInputComponent>
            </template>
            <template #footer>
                <ButtonComponent :processing="shopPackage.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.changeShopPackageWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Shop Pachage Update'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="suspendReactivate.put(route('shop.update.suspend-reactivate', {'shopid': shopid}), {
        preserveState: (page) => Object.keys(page.props.errors).length > 0,
        onSuccess: () => {
            shopStore.setState('suspendReactivate'),
            shopStore.setState('suspendReactivateWasSuccessful')
        }
    })">
        <ModalComponent v-model="shopStore.state.suspendReactivate" title="Suspend | Reactivate">
            <template #body>
                <div v-if="props.companyDetails.status != 'SUSPENDED'">
                    <InputComponent
                        v-model="suspendReactivate.date"
                        label="Trial Expiration Date"
                        iconClass="bx bx-calendar"
                        type="date"
                        :error="suspendReactivate.errors.date"
                        :required="!suspendReactivate.now"
                        :disabled="suspendReactivate.now"
                    />

                    <CustomInputComponent label="Suspend Now" inputId="suspendNow" iconClass="bx bx-user" :error="bossBoard.errors.now">
                        <template #input>
                            <span class="input-group-text">
                                <input class="form-check-input" type="checkbox" role="switch" v-model="suspendReactivate.now" name="suspendNow" id="suspendNow">
                            </span>
                            <span class="input-group-text">
                                <label class="form-check-label" for="suspendNow">
                                    Suspend Now
                                </label>
                            </span>
                        </template>
                    </CustomInputComponent>
                </div>
                <div v-else>
                    <p>This will activate all access to this shop. Are you sure?</p>
                </div>
            </template>
            <template #footer>
                <ButtonComponent :processing="suspendReactivate.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.suspendReactivateWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Suspend | Reactivate'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="bossBoard.put(route('shop.update.boss-board-access', {'shopid': shopid}), {
            onSuccess: () => {
                shopStore.setState('bossBoard')
            }
        })">
        <ModalComponent v-model="shopStore.state.bossBoard" title="Boss Board" size="medium">
            <template #body>
                <InputComponent
                    v-model="bossBoard.trialDate"
                    label="Trial Expiration Date"
                    iconClass="bx bx-calendar"
                    type="date"
                    :error="bossBoard.errors.trialDate"
                    :required="true"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="bossBoard.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="bossBoard.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Boss Board'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="dvi.put(route('shop.update.dvi', {'shopid': shopid}), {
            onSuccess: () => {
                shopStore.setState('dvi')
            }
        })">
        <ModalComponent v-model="shopStore.state.dvi" title="DVI">
            <template #body>
                <Select2Component
                    :data="['Boss Inspect','Dvi Lite']"
                    :multiple="true"
                    v-model="dvi.subscription"
                    label="Subscriptions"
                    iconClass="bx bx-box"
                    :error="dvi.errors.subscription"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="dvi.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="dvi.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'DVI'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="forceLogOut.put(route('shop.update.force-log-out', {'shopid': shopid}), {
            onSuccess: () => {
                shopStore.setState('forceLogOut')
            }
        })">
        <ModalComponent v-model="shopStore.state.forceLogOut" title="Force Log Out">
            <template #body>
                <p>This action cannot be undone. This will permanently suspend {{ props.companyDetails.companyName }} account and log out all users.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="forceLogOut.processing" name="Log Out" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="forceLogOut.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Force Log Out'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="trialExpiration.put(route('shop.update.trial-expiration', {'shopid': shopid}),{
            onSuccess: () => {
                shopStore.setState('trialExpiration')
            }
        })">
        <ModalComponent v-model="shopStore.state.trialExpiration" title="Trial Expiration">
            <template #body>
                <InputComponent
                    v-model="trialExpiration.date"
                    label="Expiration Date"
                    iconClass="bx bx-calendar"
                    type="date"
                    :error="trialExpiration.errors.date"
                    :required="true"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="trialExpiration.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="trialExpiration.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Trial Expiration'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="churn.put(route('shop.update.churn', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('churn', 'deactivate')
                shopStore.setState('churnWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.churn" title="Update Churn Date">
            <template #body>
                <CustomInputComponent label="Future Churn Date" inputId="futureChurnDate" iconClass="bx bx-calendar" :error="churn.errors.date">
                    <template #input>
                        <input type="date"  class="form-control" v-model="churn.date" :disabled="churn.now" name="futureChurnDate" id="futureChurnDate">
                    </template>
                </CustomInputComponent>

                <CustomInputComponent label="Churn Now" inputId="churnNow" iconClass="bx bx-calendar" :error="churn.errors.now">
                    <template #input>
                        <span class="input-group-text">
                            <input class="form-check-input" type="checkbox" role="switch" v-model="churn.now" name="churnNow" id="churnNow">
                        </span>
                        <span class="input-group-text">
                            <label class="form-check-label" for="churnNow">
                                Churn Now
                            </label>
                        </span>
                    </template>
                </CustomInputComponent>

                <CustomInputComponent label="Res Code" inputId="resCode" iconClass="bx bx-bug" :error="churn.errors.resCode">
                    <template #input>
                        <SelectTwoComponent
                            v-model="churn.resCode"
                            :data="churnResCodes"
                            inputId="resCode"
                            keyID="value"
                            valueID="text"
                        />
                    </template>
                </CustomInputComponent>
            </template>
            <template #footer>
                <ButtonComponent :processing="churn.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.churnWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Churn'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <ModalComponent v-model="shopStore.state.payBalanceLink" title="Pay Balance Link" size="medium">
        <template #body>
            <CustomInputComponent label="Link" inputId="payLink" iconClass="bx bx-link" :error="churn.errors.date">
                <template #input>
                    <input type="text"  class="form-control" :value="payLink" ref="payLinkInput" name="payLink" id="payLink">
                    <button class="btn btn-primary" @click="copyToClipboard(this)">{{copyButtonLabel}}</button>
                </template>
            </CustomInputComponent>
        </template>
    </ModalComponent>

    <form @submit.prevent="toggleMatco.put(route('shop.update.matco-toggle', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('toggleMatco', 'deactivate'),
                shopStore.setState('toggleShopModeWasSuccessful')
            }
        })">
        <ModalComponent v-model="shopStore.state.toggleMatco" title="Switching Shop Mode">
            <template #body>
                <p>This will switch shop to {{ shopStore.state.shopIsMatco ? "Shopboss" : "Matco" }} mode.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="toggleMatco.processing" name="Switch" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.toggleShopModeWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Switching Shop Mode'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="toggleSchool.put(route('shop.update.school-toggle', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('setSchool', 'deactivate')
                shopStore.setState('setSchoolWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.setSchool" title="Switching Set School">
            <template #body>
                <p>This will switch shop school to {{ props.companyDetails.school == 'yes' ? "off" : "on" }} mode.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="toggleSchool.processing" name="Switch" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.setSchoolWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Switching Set School'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="tpmsShopCredentials.put(route('shop.update.tpms-credentials', {'shopid': shopid}),{
            onSuccess: () => {
                shopStore.setState('tpmsShopCredentials', 'deactivate')
            }
        })">
        <ModalComponent v-model="shopStore.state.tpmsShopCredentials" title="TPMS Shop Credentials">
            <template #body>
                <InputComponent
                    v-model="tpmsShopCredentials.username"
                    placeholder="Username"
                    label="Username"
                    iconClass="bx bx-user"
                    :error="tpmsShopCredentials.errors.username"
                    :required='true'
                />

                <PasswordComponent
                    v-model="tpmsShopCredentials.password"
                    label="Password"
                    :error="tpmsShopCredentials.errors.password"
                    :required="true"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="tpmsShopCredentials.processing" name="Save" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="tpmsShopCredentials.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'TPMS Shop Credentials'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="epicor.post(route('shop.store.epicor', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('epicorLogins', 'deactivate'),
                shopStore.setState('epicorLoginsWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.epicorLogins" title="Epicor Login">
            <template #body>
                <Select2Component
                    :data="props.epicorSuppliersList"
                    v-model="epicor.supplier"
                    label="Supplier"
                    iconClass="bx bx-briefcase"
                    :error="epicor.errors.supplier"
                    :required="true"
                />

                <InputComponent
                    v-model="epicor.sellerID"
                    placeholder="Seller ID"
                    label="Seller ID"
                    iconClass="bx bx-tag"
                    :error="epicor.errors.sellerID"
                    :required='true'
                />

                <InputComponent
                    v-model="epicor.group"
                    placeholder="Group"
                    label="Group"
                    iconClass="bx bx-category"
                    :error="epicor.errors.group"
                    :required='true'
                />

                <InputComponent
                    v-model="epicor.username"
                    placeholder="Username"
                    label="Username"
                    iconClass="bx bx-user"
                    :error="epicor.errors.username"
                    :required='true'
                />

                <PasswordComponent
                    v-model="epicor.password"
                    label="Password"
                    :error="epicor.errors.password"
                    :required="true"
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="epicor.processing" name="Save" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.epicorLoginsWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Epicor'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>


    <form @submit.prevent="addCardknoxKeys.put(route('shop.update.cardknox-keys', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('addCardknoxKeys', 'deactivate'),
                shopStore.setState('addCardknoxKeysWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.addCardknoxKeys" title="Cardknox Keys">
            <template #body>
                <InputComponent
                    v-model="addCardknoxKeys.cardknoxKey"
                    placeholder="Cardknox Key"
                    label="Cardknox Key"
                    iconClass="bx bx-key"
                    :error="addCardknoxKeys.errors.cardknoxKey"
                    :required='true'
                />

                <InputComponent
                    v-model="addCardknoxKeys.ifieldsKey"
                    placeholder="IFields Key"
                    label="IFields Key"
                    iconClass="bx bx-key"
                    :error="addCardknoxKeys.errors.ifieldsKey"
                    :required='true'
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="addCardknoxKeys.processing" name="Save" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.addCardknoxKeysWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Cardknox'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="activateCardknox.put(route('shop.update.cardknox-activate', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('activateCardknox', 'deactivate'),
                shopStore.setState('activateCardknoxWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.activateCardknox" title="Activate CardKnox">
            <template #body>
                <p>This action cannot be undone. This will permanently activate Cardknox integration.</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="activateCardknox.processing" name='Activate'/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.activateCardknoxWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Cardknox'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="addCardknoxTerminal.put(route('shop.update.cardknox-terminal', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('addCardknoxTerminal', 'deactivate'),
                shopStore.setState('addCardknoxTerminalWasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.addCardknoxTerminal" title="Cardknox Terminal">
            <template #body>
                <InputComponent
                    v-model="addCardknoxTerminal.ip"
                    placeholder="IP Address"
                    label="IP Address"
                    iconClass="bx bx-network-chart"
                    :error="addCardknoxTerminal.errors.ip"
                    :required='true'
                />

                <InputComponent
                    v-model="addCardknoxTerminal.name"
                    placeholder="Name"
                    label="Name"
                    iconClass="bx bx-label"
                    :error="addCardknoxTerminal.errors.name"
                    :required='true'
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="addCardknoxTerminal.processing" name="Save" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.addCardknoxTerminalWasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Cardknox'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="payments360.put(route('shop.update.payments360', {'shopid': shopid}), {
            preserveState: (page) => Object.keys(page.props.errors).length > 0,
            onSuccess: () => {
                shopStore.setState('payments360Modal', 'deactivate'),
                shopStore.setState('payments360WasSuccessful', 'activate')
            }
        })">
        <ModalComponent v-model="shopStore.state.payments360Modal" title="360 Payments">
            <template #body>
                <InputComponent
                    v-model="payments360.terminalID"
                    placeholder="Force Terminal ID"
                    label="Force Terminal ID"
                    iconClass="bx bx-terminal"
                    :error="payments360.errors.terminalID"
                    :required='true'
                />

                <InputComponent
                    v-model="payments360.token"
                    placeholder="Force Payment Token"
                    label="Force Payment Token"
                    iconClass="bx bx-key"
                    :error="payments360.errors.token"
                    :required='true'
                />

                <Select2Component
                    :data="['360','360-3PP']"
                    v-model="payments360.type"
                    label="Type"
                    iconClass="bx bx-list-ul"
                    :error="payments360.errors.type"
                    :required="true"
                />

                <InputComponent
                    v-model="payments360.cfpid"
                    placeholder="CFPID"
                    label="CFPID"
                    iconClass="bx bx-fingerprint"
                    :error="payments360.errors.cfpid"
                    :required='true'
                />
            </template>
            <template #footer>
                <ButtonComponent :processing="payments360.processing" name="Save" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="shopStore.state.payments360WasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'360 Payments'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>


    <form @submit.prevent="betaFeatures.put(route('shop.update.beta-features', { shopid: shopid }), {
            onSuccess: () => shopStore.setState('betaFeaturesModal')
        })">
        <ModalComponent v-model="shopStore.state.betaFeaturesModal" title="Beta Features">
            <template #body>
                <div class="container mb-3">
                    <div class="row">
                        <div v-for="feature in props.betaFeatures.list" :key="feature.id" class="col-md-6">
                            <div class="form-check">
                                <input
                                    class="form-check-input"
                                    type="checkbox"
                                    :value="feature.id"
                                    v-model="betaFeatures.selectedFeatures"
                                    :id="'feature-' + feature.id"
                                    style="width: 1.05rem; height: 1.05rem; transform: translate(-1px, 2px);"
                                />
                                <label class="form-check-label fs-6" :for="'feature-' + feature.id">
                                    {{ feature.feature_name }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </template>


            <template #footer>
                <ButtonComponent :processing="betaFeatures.processing" class="primary"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="betaFeatures.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Beta Features'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>

    <form @submit.prevent="switchToPaid.put(route('shop.update.paid', { shopid: shopid }), {
            onSuccess: () => {
                shopStore.setState('switchToPaidModal')
                shopStore.setState('shopIsTrial', 'deactivate')
            }
        })">
        <ModalComponent v-model="shopStore.state.switchToPaidModal" title="Swtich To Paid">
            <template #body>
                <p>Are you sure you want to upgrade {{ props.companyDetails.companyName }} to a paid plan?</p>
            </template>
            <template #footer>
                <ButtonComponent :processing="switchToPaid.processing"/>
            </template>
        </ModalComponent>

        <BaseAlertComponent v-if="switchToPaid.wasSuccessful"
            :messageType="$page.props.flash.message ? 'success' : 'error'"
            :title="'Beta Features'"
            :message="$page.props.flash.message || $page.props.flash.error"
        />
    </form>
</template>

<script setup>
    import { watch, ref, reactive } from 'vue'
    import { usePage, useForm } from '@inertiajs/vue3'
    import InputComponent from '@/components/utils/InputComponent.vue'
    import PasswordComponent from '@/components/utils/PasswordComponent.vue'
    import CustomInputComponent from '@/components/utils/CustomInputComponent.vue'
    import ModalComponent from '@/components/utils/ModalComponent.vue'
    import SelectTwoComponent from '@/components/utils/SelectTwoComponent.vue'
    import Select2Component from '@/components/utils/Select2Component.vue'
    import ButtonComponent from '@/components/utils/ButtonComponent.vue'
    import BaseAlertComponent from '@/components/alerts/BaseAlertComponent.vue';

    import { useShopStore } from '@/stores/shop/shopStore'
    const shopStore = useShopStore()

    const { props } = usePage()

    const shopid = props.shopid

    const startingRepairOrderNumber = useForm({
        roid: ""
    })
    watch(() => startingRepairOrderNumber.wasSuccessful, (newVal) => {
        if (newVal) {
            startingRepairOrderNumber.roid = "";
        }
    });


    const partTechs = ref(props.partTech)

    const partsTech = useForm({
        userId: "",
        apiKey: "",
        type: "Standard",
        success: false
    })

    const partsTechWasSuccessful = ref(false)
    const partsTechDeleteWasSuccessful = ref(false)

    const deletePartsTech = useForm({
        id: null,
        name: null
    })

    const deletePartsTechSupplier = (id, name) => {
        deletePartsTech.id = id
        deletePartsTech.name = name
        shopStore.setState('deletePartsTech')
    }

    const readOnly = useForm({
        now: false,
        future: ""
    })

    const shopPackage = useForm({
        package: props.companyDetails.newpackagetype,
        trialExpiration: "",
        future: "",
        futureOn: false,
        trialOn: false
    })

    const reactiveShopPackage = reactive(shopPackage);

    watch(() => reactiveShopPackage, (newVal) => {
        if ( newVal.futureOn ) shopPackage.future = ""
        if( newVal.trialOn ) shopPackage.trialExpiration = ""
    }, { deep: true });

    const suspendReactivate = useForm({
        now: true,
        date: "",
        active: props.companyDetails.status == 'ACTIVE'
    })

    const linkedShops = useForm({
        shops: props.joinedShops,
    })

    const bossBoard = useForm({
        trialDate: props?.companyDetails?.dashExpiration
        ? new Date(props.companyDetails.dashExpiration).toISOString().split('T')[0]
        : ""
    })

    const dvi = useForm({
        subscription: props.companyAdds
    })

    const forceLogOut = useForm({})

    shopStore.setState('shopPackageIsTrial', props.companyDetails.package == 'Paid' ? 'deactivate' : 'activate')

    const trialExpiration = useForm({
        date: props.companyDetails.trialexpiration
    })

    const churnResCodes = [
        { value: 'Lost Budget,No ROI', text: 'Cost' },
        { value: 'Better Features,Better Integration,Lower Cost,Better Support', text: 'Went to a Competitor' },
        { value: 'DealerSocket iDMS Early Termination,DealerSocket iDMS Suite Cancelation', text: 'Partner/Reseller Cancellation' },
        { value: 'Better Features,Lacked Product Innovation,Product Malfunctioned/Defect,Product Performance/Stability,Product Sunset,Support/Service Issues', text: 'Product Issues' },
        { value: 'Business Closed', text: 'Business Closed' },
        { value: 'Sold/Changed Owners', text: 'Sold/Changed Owners' },
        { value: 'Support/Service Issues', text: 'Support/Service Issues' },
        { value: 'Never Went Live', text: 'Never Went Live' },
        { value: 'Credit Card Declined', text: 'Credit Card Declined' },
        { value: 'Replacing with new upsell Opp', text: 'Replacing with new upsell Opp' },
        { value: 'Replacing with new add-on Opp', text: 'Replacing with new add-on Opp' },
        { value: 'Consolidating business', text: 'Consolidating business' },
        { value: 'Bill incorrectly', text: 'Bill incorrectly' },
        { value: 'Replacing with new Downgrad Opp', text: 'Replacing with new Downgrad Opp' },
        { value: 'Unknown', text: 'Unknown' },
    ]

    const churn = useForm({
        date: "",
        resCode: "",
        now: false,
    })


    const payLink = ref()

    const getPaylink = async () => {
        try {
            const response = await axios.get(route('shop.get.pay-link', { shopid: shopid }));

            payLink.value = response.data.payLink
        } catch (error) {
            console.error('Error fetching paylink:', error);
        }
    };
    getPaylink()

    var copyButtonLabel = ref('Copy')
    function copyToClipboard(){
        navigator.clipboard.writeText(payLink.value)

        copyButtonLabel.value = 'Copied'

        setTimeout(() => {
            copyButtonLabel.value = 'Copy'
        }, 3000);
    }

    shopStore.state.shopIsMatco = props.companyDetails.matco == "yes" ? true : false
    shopStore.state.shopIsTrial = props.companyDetails.package == 'Paid' ? false : true

    const toggleMatco = useForm({})
    const toggleSchool = useForm({})

    const tpmsShopCredentials = useForm({
        username: "",
        password: ""
    })

    const epicor = useForm({
        supplier: "",
        sellerID: "",
        group: "",
        username: "",
        password: ""
    })

    const addCardknoxKeys = useForm({
        cardknoxKey: "",
        ifieldsKey: ""
    })

    const activateCardknox = useForm({})

    const addCardknoxTerminal = useForm({
        ip: "",
        name: ""
    })

    const payments360 = useForm({
        terminalID: props.companyDetails.terminalid,
        token: props.companyDetails.paytoken,
        type: props.companyDetails.creditCardProcessor,
        cfpid: props.companyDetails.cfpid,
    })

    const betaFeatures = useForm({
        selectedFeatures: props.betaFeatures.shop
    })

    const switchToPaid = useForm({})
</script>

<style scoped>
    .custom-input{
        position: relative;
        flex: 1 1 auto;
        width: 1%;
        min-width: 0;
    }

    .form-check-input{
        margin-top: 0
    }

    .partsTech {
        overflow-x: auto;
        padding-bottom: 10px;
        margin-bottom: 10px;
        width: 100%;
    }

    .partsTech .header,
    .partsTech .data .item{
        display: grid;
        align-items: center;
        grid-template-columns: 100px 150px 1fr 100px;
    }

    .partsTech .header p,
    .partsTech .data .item p{
        margin: 0 10px;
    }
    .partsTech .data .item{
        margin: 10px 0;
    }
</style>
