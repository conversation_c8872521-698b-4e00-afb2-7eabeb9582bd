<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" route="/dashboard/report/accounting/datatable"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'Accounting Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/accounting'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'contact', name: 'contact', label: 'Contact'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name', width: 200},
            {data: 'address', name: 'address', label: 'Company Address', width: 200},
            {data: 'CompanyEMail', name: 'companyemail', label: 'Company Email'},
            {data: 'CompanyPhone', name: 'CompanyPhone', label: 'Company Phone', width: 90},
            {data: 'billing', name: 'billing', label: 'Billing Address', width: 200},
            {data: 'merchantaccount', name: 'merchantaccount', label: 'Merchant'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'flatprice', name: 'flatprice', label: 'Flat Price'},
        ]
    );
</script>
