<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="d-flex flex-row-reverse mb-3">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
            </div>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import { ref, computed } from 'vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, calculateElapsedTime, formatDate, formatPhoneNumber } from '@/utils/utils.js'

    const pageTitle = 'Churn Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('churn.report.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Name'},
            {data: 'contact', name: 'contact', label: 'Contact', width: 90,},
            {data: 'CompanyPhone', name: 'CompanyPhone', label: 'Phone', width: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.CompanyPhone ? formatPhoneNumber(row.CompanyPhone) : '';
                    }
                    return row.CompanyPhone || '';
                }
            },
            {data: 'CompanyEmail', name: 'CompanyEmail', label: 'Email'},
            {data: 'datein', name: 'datein', label: 'Last RO', width: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datein ? (row.datein) : '';
                    }
                    return row.datein || '';
                }
            },
            {data: 'roCount', name: 'roCount', label: 'Closed Count', searchable: false, orderable: false},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'Date Started',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'datesuspended', name: 'datesuspended', label: 'Date Suspended',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datesuspended ? formatDate(row.datesuspended) : '';
                    }
                    return row.datesuspended || '';
                }
            },
            {
                data: '', name: '', label: 'Elapsed',
                render: function (data, type, row) {
                    if (type === 'display') {
                        if (row.dateofacceptance && row.datesuspended) {
                            return calculateElapsedTime(row.dateofacceptance, row.datesuspended);
                        }
                        return '';
                    }
                    return '';
                }
            },
            {
                data: 'shopid',
                name: 'link',
                label: 'Comments',
                render: function(data) {
                    return `<a href="/dashboard/report/churn/${data}" class="btn btn-primary">View</a>`;
                },
                orderable: false,
                searchable: false
            }
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/churn/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
