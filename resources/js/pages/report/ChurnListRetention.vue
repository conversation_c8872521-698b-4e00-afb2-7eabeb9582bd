<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute" />
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'Churn List Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/churn/list/retention'
        }
    ]

    const columns = ref(
        [
            {data: 'CompanyName', name: 'CompanyName', label: 'Shop Name'},
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'category', name: 'category', label: 'Category'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'salesrep', name: 'salesrep', label: 'Salesman'},
            {data: 'flatprice', name: 'flatprice', label: 'MRR'},
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'LPD', width: 60,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastpaymentdate ? formatDate(row.lastpaymentdate) : '';
                    }
                    return row.lastpaymentdate || '';
                }
            },
            {data: 'datestarted', name: 'datestarted', label: 'Date Started', width: 60,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A.', width: 70,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'churndate', name: 'churndate', label: 'D.O.C.', width: 70,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.churndate ? formatDate(row.churndate) : '';
                    }
                    return row.churndate || '';
                }
            },
            {data: 'whochurned', name: 'whochurned', label: 'Who Churned'},
            {data: 'churnreason', name: 'churnreason', label: 'Res Code'},
            {data: 'vhc_number', name: 'vhc_number', label: 'VHC Number'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('churn.list.retention.datatable', {start_date: startDate, end_date: endDate});
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
