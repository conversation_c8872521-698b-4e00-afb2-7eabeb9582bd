<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <div class="form-check pe-3">
                <input class="form-check-input" type="checkbox" v-model="sharedReportsStore.state.sharedReports">
                <label class="form-check-label" for="flexCheckDefault">
                    Shared Reports
                </label>
            </div>
        </div>

        <CustomReportsComponent v-if="!sharedReportsStore.state.sharedReports"/>
        <SharedReportsComponent v-if="sharedReportsStore.state.sharedReports" :sharedReports="props.sharedDirectories" :companies="props.companies"/>
    </DashboardLayout>
</template>

<script setup>
    import { usePage } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import CustomReportsComponent from '@/components/reports/CustomReportsComponent.vue';
    import SharedReportsComponent from '@/components/reports/SharedReportsComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { useSharedReportsStore } from '@/stores/shop/sharedReportsStore'

    const sharedReportsStore = useSharedReportsStore()

    const pageTitle = 'Custom Reports';

    const { props } = usePage();

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/shop/category'
        }
    ]
</script>
