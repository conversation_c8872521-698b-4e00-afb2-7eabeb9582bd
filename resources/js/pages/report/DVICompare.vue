<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" subtitle="This Report Compares DVIs and ARO Between DVI Lite and Boss Inspect" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute" :serverSide="true"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'DVI Comparison Report for SS Shop Boss Users';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('dvi.compare.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companystate', name: 'companystate', label: 'State'},
            {data: 'companyzip', name: 'companyzip', label: 'ZIP'},
            {data: 'country', name: 'country', label: 'Country', orderable: false, searchable: false,},
            {data: 'boss_inspect', name: 'boss_inspect', label: 'Boss Inspect', orderable: false, searchable: false,},
            {data: 'dvi_lite', name: 'dvi_lite', label: 'DVI Lite', orderable: false, searchable: false,},
            {data: 'totalRO', name: 'totalRO', label: 'ARO', orderable: false, searchable: false,},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/dvi/compare/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
