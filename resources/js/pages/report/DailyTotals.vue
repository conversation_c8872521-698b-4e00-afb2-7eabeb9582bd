<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" route="/dashboard/report/daily/totals/datatable"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { asDollars,formatDate } from '@/utils/utils.js'

    const pageTitle = 'Daily Totals Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('daily.totals.index')
        }
    ]

    const columns = ref(
        [
            {data: 'day_of_week', name: 'day_of_week', label: 'Day'},
            {data: 'StatusDate', name: 'StatusDate', label: 'Inv Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.StatusDate ? formatDate(row.StatusDate) : '';
                    }
                    return row.StatusDate || '';
                }
            },
            {
                data: 'ammount',
                name: 'ammount',
                label: 'Gross Sale',
                render: function (data, type, row) {
                    return asDollars(row.ammount);
                }
            },
            {data: 'count', name: 'count', label: 'RO Count'},
            {
                data: null,
                name: 'amount_per_count',
                label: 'ARO',
                orderable: false,
                searchable: false,
                render: function (data, type, row) {
                    return row.count > 0 ? asDollars(row.ammount / row.count) : 'N/A';
                }
            },
        ]
    );
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
