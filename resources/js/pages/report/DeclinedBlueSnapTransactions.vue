<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import { ref, computed } from 'vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, calculateElapsedTime, formatDate } from '@/utils/utils.js'

    const pageTitle = 'Declined BlueSnap Transactions Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('declined.transactions.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'company_name', name: 'company_name', label: 'Shop Name'},
            {data: 'company_phone', name: 'company_phone', label: 'Shop Phone'},
            {data: 'company_email', name: 'company_email', label: 'Company Email'},
            {data: 'Transaction Date', name: 'Transaction Date', label: 'Tx Date', width:70},
            {data: 'card_owner', name: 'card_owner', label: 'Card Owner'},
            {data: 'Bin Issuer Organization', name: 'Bin Issuer Organization', label: 'Card Issuer'},
            {data: 'card_type', name: 'card_type', label: 'Card Type'},
            {data: 'Last 4 Digits', name: 'Last 4 Digits', label: 'Card Last4'},
            {data: 'Amount (USD)', name: 'Amount (USD)', label: 'Amount'},
            {data: 'Decline Reason', name: 'Decline Reason', label: 'Decline Reason'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/declined/transactions/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
