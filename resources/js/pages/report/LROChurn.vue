<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'LRO Churn Report';

    const pageLocation = [
        {
            name: 'Report',
            link: route('report.index')
        },
        {
            name: pageTitle,
            link: route('lro.churn.index')
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'churndate', name: 'churndate', label: 'Churn Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.churndate ? formatDate(row.churndate) : '';
                    }
                    return row.churndate || '';
                }
            },
            {data: 'lastRODateIn', name: 'lastRODateIn', label: 'LRO Date',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastRODateIn ? formatDate(row.lastRODateIn) : '';
                    }
                    return row.lastRODateIn || '';
                }
            },
            {data: 'recentPaymentDate', name: 'recentPaymentDate', label: 'LPD',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.recentPaymentDate ? formatDate(row.recentPaymentDate) : '';
                    }
                    return row.recentPaymentDate || '';
                }
            },
            {data: 'roCount', name: 'roCount', label: 'RO Count'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/lro/churn/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
