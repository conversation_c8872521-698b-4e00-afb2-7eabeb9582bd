<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange"/>

        <div class="bandwith-messages d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>

            <div class="align-items-right">
                <DatePicker :date="dateRange" @update:dateRange="updateDateRange" />

                <div>
                    <label for="">Status</label>
                    <select class="form-select" v-model="filter.messageStatus">
                        <option v-for="status in messageStatuses" :key="status" :value="status">
                            {{ status }}
                        </option>
                    </select>
                </div>
            </div>
        </div>

        <pre>{{ filter.startDate }}</pre>

        <MandrillMessagesTableComponent
            :startDate="filter.startDate"
            :endDate="filter.endDate"
            :messageStatus="filter.messageStatus"
        />
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import MandrillMessagesTableComponent from '@/components/reports/MandrillMessagesTableComponent.vue';
    import DatePicker from '@/components/utils/DatePicker.vue';
    import { getCurrentMonthRange, getToday, formatDate } from '@/utils/utils.js';
    import axios from 'axios';

    const pageTitle = 'Mandrill Messages Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/bandwith-messages'
        }
    ];

    const dateRange = ref(getToday('us'));
    const reportDateRange = ref(getToday());

    const filter = ref({
        startDate: reportDateRange.value[0],
        endDate: reportDateRange.value[1],
        messageStatus: "All"
    })

    const messageStatuses = [
        'All',
        'Delivered',
        'Undelivered'
    ];

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;

        filter.value.startDate = newDateRange[0];
        filter.value.endDate = newDateRange[1];
    };

    // axios.post('https://mandrillapp.com/api/1.0/messages/search',
    //     {
    //         key: 'md-UYhNcWBIpF6LDyQeALnA8w',

    //     }
    // ).then(response => {
    //     console.log(response.data);
    // })
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }

    .bandwith-messages .align-items-right{
        display: flex;
        justify-content: flex-end;
        align-items: end;
    }

    .bandwith-messages .input-component{
        display: block;
    }

    .bandwith-messages .align-items-right div:nth-child(2){
        margin: 0 10px;
    }

    .bandwith-messages .search .mb-3{
        margin: 0 !important;
    }

    .bandwith-messages .search div{
        margin: 0 !important;
    }

    .bandwith-messages .custom-input {
        display: flex;
        overflow: hidden;
        width: 100%;
        border: 1px solid #ccc;
        border-radius: 5px;
        height: 38px;
    }

    .bandwith-messages .custom-input .form-select {
        border: none;
        padding: 0 10px;
        width: 200px;
        text-align: left;
        cursor: pointer;
        height: 100%;
        appearance: none;
        -webkit-appearance: none;
        -moz-appearance: none;
        position: relative;

        /* Add dropdown arrow */
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right 10px center;
        background-size: 14px;
    }

    .bandwith-messages .custom-input .form-control {
        border: none;
        flex: 1;
        padding: 8px 10px;
        outline: none;
        height: 100%;
    }
</style>
