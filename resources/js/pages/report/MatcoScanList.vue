<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="'Matco Scan List'" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = 'Matco Scan List Repssort';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/matco/scan/list'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'contact', name: 'contact', label: 'Contact'},
            {data: 'CompanyName', name: 'CompanyName', label: 'Company Name'},
            {data: 'address', name: 'address', label: 'Company Address'},
            {data: 'CompanyEMail', name: 'CompanyEMail', label: 'Company Email'},
            {data: 'CompanyPhone', name: 'CompanyPhone', label: 'Company Phone', width: 90},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A.', width: 60,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: '', name: '', label: 'DVI Count',
                render: function (data, type, row) {
                    return row.bicount + row.dvicount;
                }
            },
            {data: 'scancount', name: 'scancount', label: 'Scan Count'},
            {data: 'allscancount', name: 'allscancount', label: 'All Scan Count'},
            {data: 'datestarted', name: 'datestarted', label: 'Date Started',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package Type'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/matco/scan/list/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
