<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute" />
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate, formatPhoneNumber } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = "Motor Usage Report With ProDemand & Shop Category";

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/motor/usage'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'status', name: 'status', label: 'Status'},
            {data: 'prodemand', name: 'prodemand', label: 'Prodemand'},
            {data: 'category', name: 'category', label: 'Category'},
            {data: 'datestarted', name: 'datestarted', label: 'Date Started',
                render: function (data, type,row) {
                    return row.datestarted ? formatDate(row.datestarted) : '';
                }
            },
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'Last Payment',
                render: function (data, type, row) {
                    return row.lastpaymentdate ? formatDate(row.lastpaymentdate) : '';
                }
            },
            {data: 'churndate', name: 'churndate', label: 'Churn Date',
                render: function (data, type, row) {
                    return row.churndate ? formatDate(row.churndate) : '';
                }
            },
            {data: 'month', name: 'month', label: 'Month'},
            {data: 'total_clicks', name: 'total_clicks', label: 'Total Clicks'},
            {data: 'contact', name: 'contact', label: 'Contact'},
            {data: 'companyemail', name: 'companyemail', label: 'Email'},
            {data: 'companyphone', name: 'companyphone', label: 'Phone', width: 90,
                render: function (data, type, row) {
                    return row.companyphone ? formatPhoneNumber(row.companyphone) : '';
                }
            },
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return route('motor.usage.prodemand.datatable', {start_date: startDate, end_date: endDate});
    });
</script>

<style scoped>
    tr th{
        min-width: 100px;
    }
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
