<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" :route="route('readonly.shops.datatable')"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'Read Only Shops Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/readonly/shops'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'companyaddress', name: 'companyaddress', label: 'Company Address'},
            {data: 'companyCSZ', name: 'companyCSZ', label: 'Company CSZ'},
            {data: 'companyphone', name: 'companyphone', label: 'Company Phone'},
            {data: 'companyemail', name: 'companyemail', label: 'Company Email'},
            {data: 'contact', name: 'contact', label: 'Contact', width: 90},
            {data: 'merchantaccount', name: 'merchantaccount', label: 'Merchant Account'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'status', name: 'status', label: 'Status'},
            {data: 'matco', name: 'matco', label: 'Matco'},
            {data: 'readonly', name: 'readonly', label: 'Read Only'},
            {data: 'demo', name: 'demo', label: 'Demo'},
            {data: 'churn', name: 'churn', label: 'Churn'},
            {data: 'churn_date', name: 'churn_date', label: 'Churn Date'},
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'L.P.D.'},
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A.'},
            {data: 'datestarted', name: 'datestarted', label: 'Date Started'},
            {data: 'last_login_date', name: 'last_login_date', label: 'Last Login Date'},
            {data: 'user_account_that_logged_in_last', name: 'user_account_that_logged_in_last', label: 'Last Login User'},
            {data: 'vhc_number', name: 'vhc_number', label: 'VHC Number'},
        ]
    );
</script>
