<template>
    <head>
        <title>ShopBoss - {{ pageTitle }}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle" :dateRange="reportDateRange" :key="computedRoute"/>

        <div class="d-flex justify-content-between align-items-center mb-2">
            <PageLocation :items="pageLocation"/>
            <DatePicker :date="dateRange" @update:dateRange="updateDateRange"/>
        </div>

        <ReactiveReportComponent :columns="columns" :route="computedRoute" :key="computedRoute" />
    </DashboardLayout>
</template>

<script setup>
    import { ref, computed } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import { getCurrentMonthRange, formatDate } from '@/utils/utils.js'
    import DatePicker from '@/components/utils/DatePicker.vue';

    const pageTitle = "Suspended Shop's List Report";

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/suspended/shops/list'
        }
    ]

    const columns = ref(
        [
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'flatprice', name: 'flatprice', label: 'MRR'},
            {data: 'lastpaymentdate', name: 'lastpaymentdate', label: 'LPD',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastpaymentdate ? formatDate(row.lastpaymentdate) : '';
                    }
                    return row.lastpaymentdate || '';
                }
            },
            {data: 'datestarted', name: 'datestarted', label: 'Date Started',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'dateofacceptance', name: 'dateofacceptance', label: 'D.O.A.',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.dateofacceptance ? formatDate(row.dateofacceptance) : '';
                    }
                    return row.dateofacceptance || '';
                }
            },
            {data: 'datesuspended', name: 'datesuspended', label: 'Suspended Date'},
        ]
    );

    const dateRange = ref(getCurrentMonthRange('us'));
    const reportDateRange = ref(getCurrentMonthRange());

    const updateDateRange = (newDateRange) => {
        dateRange.value = newDateRange.map(date => formatDate(date));
        reportDateRange.value = newDateRange;
    };

    const computedRoute = computed(() => {
        const [startDate, endDate] = reportDateRange.value;
        return `/dashboard/report/suspended/shops/list/datatable?start_date=${startDate}&end_date=${endDate}`;
    });
</script>

<style scoped>
    td .multiline {
        white-space: pre-line;
    }
    .btn {
        margin-bottom: 10px;
    }
</style>
