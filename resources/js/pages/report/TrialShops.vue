<template>
    <head>
        <title>ShopBoss - {{pageTitle}}</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="pageTitle"/>
        <PageLocation :items="pageLocation"/>
        <ReactiveReportComponent :columns="columns" route="/dashboard/report/trial/shops/datatable"/>
    </DashboardLayout>
</template>

<script setup>
    import { ref } from 'vue';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import ReactiveReportComponent from '@/components/reports/ReactiveReportComponent.vue';
    import PageLocation from '@/components/utils/PageLocation.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';

    const pageTitle = 'Trial Shops Report';

    const pageLocation = [
        {
            name: 'Report',
            link: '/dashboard/report'
        },
        {
            name: pageTitle,
            link: '/dashboard/report/shop/category'
        }
    ]

    const columns = ref(
        [
            {data: 'shopid', name: 'shopid', label: 'Shop ID'},
            {data: 'companyname', name: 'companyname', label: 'Company Name'},
            {data: 'contact', name: 'contact', label: 'Contact', width: 90},
            {data: 'companystate', name: 'companystate', label: 'State'},
            {data: 'companyphone', name: 'companyphone', label: 'Phone', width: 70},
            {data: 'companyemail', name: 'companyemail', label: 'Email'},
            {data: 'newpackagetype', name: 'newpackagetype', label: 'Package'},
            {data: 'datestarted', name: 'datestarted', label: 'Started On',
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.datestarted ? formatDate(row.datestarted) : '';
                    }
                    return row.datestarted || '';
                }
            },
            {data: 'lastRODate', name: 'lastRODate', label: 'Last RO', width: 90,
                render: function (data, type, row) {
                    if (type === 'display') {
                        return row.lastRODate ? formatDate(row.lastRODate) : '';
                    }
                    return row.lastRODate || '';
                }
            },
            {data: 'roCount', name: 'roCount', label: 'RO Count'},
            {data: 'totalRO', name: 'totalRO', label: 'Revenue'},
        ]
    );
</script>
