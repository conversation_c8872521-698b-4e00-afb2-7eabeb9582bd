<template>
    <head>
        <title>ShopBoss - Shop Detail</title>
    </head>

    <DashboardLayout>
        <TitleComponent :title="'Shop Detail Page'" />

        <div class="mb-2">
            <ChooseShop
                :name="'Choose Shop'"
                :items="props.companies"
                @update:active="shopUpdate"
            />
        </div>

        <div class="custom-grid">
            <TableComponent
                :data="latestShopsData"
                :columns="latestShopsColumns"
                :title="'Last 10 Shops'"
                :subtitle="'Displays the 10 most recently added shops'"
                :loading="loadingLatestShops"
            />

            <TableComponent
                :data="oldestShopsData"
                :columns="latestShopsColumns"
                :title="'Oldest 10 Shops'"
                :subtitle="'Displays the 10 shops added the longest time ago'"
                :loading="loadingOldestShops"
            />
        </div>
        <div class="custom-grid-2">
            <TableComponent
                :data="gainersData"
                :columns="gainersOrLoosersColumns"
                :title="'Top 10 Gainers'"
                :subtitle="'Displays the top 10 gainers based on the total revenue'"
                :loading="loadingGainers"
            />

            <TableComponent
                :data="loosersData"
                :columns="gainersOrLoosersColumns"
                :title="'Top 10 Losers'"
                :subtitle="'Displays the top 10 losers based on the total revenue'"
                :loading="loadingLosers"
            />
        </div>

    </DashboardLayout>
</template>

<script setup>
    import { ref, onMounted } from 'vue';
    import { usePage, router } from '@inertiajs/vue3';
    import DashboardLayout from '@/layouts/DashboardLayout.vue';
    import TitleComponent from '@/components/utils/TitleComponent.vue';
    import ChooseShop from '@/components/shop/ChooseShop.vue';
    import TableComponent from '@/components/table/TableComponent.vue';
    import axios from 'axios';
    import { route } from 'ziggy-js';

    const { props } = usePage();

    const latestShopsData = ref([]);
    const oldestShopsData = ref([]);
    const loadingLatestShops = ref(true);
    const loadingOldestShops = ref(true);

    const latestShopsColumns = [
        { data: 'shopid', label: 'Shop ID', type: 'text', link: true, baseLink: '/dashboard/shop/'},
        { data: 'companyName', label: 'Company Name', type: 'text'},
        { data: 'companyPhone', label: 'Company Phone', type: 'phone'},
        { data: 'newpackagetype', label: 'Package', type: 'text'},
        { data: 'dateStarted', label: 'Date Started', type: 'date'},
    ];

    const gainersData = ref([]);
    const loosersData = ref([]);
    const loadingGainers = ref(true);
    const loadingLosers = ref(true);

    const gainersOrLoosersColumns = [
        { data: 'shopid', label: 'Company ID', type: 'text', link: true, baseLink: '/dashboard/shop/'},
        { data: 'companyName', label: 'Company Name', type: 'text'},
        { data: 'companyPhone', label: 'Company Phone', type: 'phone'},
        { data: 'newpackagetype', label: 'Package', type: 'text'},
        { data: 'totalRO', label: 'Total RO', type: 'money'},
        { data: 'count', label: 'Count', type: 'text'},
    ];

    async function getGainerOrLoosers(type) {
        try {
            const res = await axios.get(route('shop.gainers-loosers'), {
                params: {
                    gainers: type
                }
            });

            if (type) {
                gainersData.value = res.data;
                loadingGainers.value = false;
            } else {
                loosersData.value = res.data;
                loadingLosers.value = false;
            }
        } catch (errors) {
            console.error(errors);
        }
    }

    async function getNewOrOld(type) {
        try {
            const res = await axios.get(route('shop.new-old'), {
                params: {
                    new: type
                }
            });

            if (type) {
                latestShopsData.value = res.data;
                loadingLatestShops.value = false;
            } else {
                oldestShopsData.value = res.data;
                loadingOldestShops.value = false;
            }
        } catch (errors) {
            console.error(errors);
        }
    }

    onMounted(() => {
        getNewOrOld(true);
        getNewOrOld(false);

        getGainerOrLoosers(true);
        getGainerOrLoosers(false);
    });

    function shopUpdate(shopid) {
        const url = `/dashboard/shop/${shopid}`;
        router.get(url);
    }
</script>


<style scoped>
    .custom-grid,
    .custom-grid-2{
        --gap: 30px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: var(--gap);
        margin-bottom: var(--gap);
    }

    @media only screen and (max-width:1800px){
        .custom-grid{
            grid-template-columns: 1fr;
        }
    }

    @media only screen and (max-width:2100px){
        .custom-grid-2{
            grid-template-columns: 1fr;
        }
    }

    .card{
        margin: 0;
    }
</style>
